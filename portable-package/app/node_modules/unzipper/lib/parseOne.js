var Stream = require('stream');
var Parse = require('./parse');
var duplexer2 = require('duplexer2');
var BufferStream = require('./BufferStream');

// Backwards compatibility for node versions < 8
if (!Stream.Writable || !Stream.Writable.prototype.destroy)
  Stream = require('readable-stream');

function parseOne(match,opts) {
  var inStream = Stream.PassThrough({objectMode:true});
  var outStream = Stream.PassThrough();
  var transform = Stream.Transform({objectMode:true});
  var re = match instanceof RegExp ? match : (match && new RegExp(match));
  var found;

  transform._transform = function(entry,e,cb) {
    if (found || (re && !re.exec(entry.path))) {
      entry.autodrain();
      return cb();
    } else {
      found = true;
      out.emit('entry',entry);
      entry.on('error',function(e) {
        outStream.emit('error',e);
      });
      entry.pipe(outStream)
        .on('error',function(err) {
          cb(err);
        })
        .on('finish',function(d) {
          cb(null,d);
        });
    }
  };

  inStream.pipe(Parse(opts))
    .on('error',function(err) {
      outStream.emit('error',err);
    })
    .pipe(transform)
    .on('error',Object)  // Silence error as its already addressed in transform
    .on('finish',function() {
      if (!found)
        outStream.emit('error',new Error('PATTERN_NOT_FOUND'));
      else
        outStream.end();
    });

  var out = duplexer2(inStream,outStream);
  out.buffer = function() {
    return BufferStream(outStream);
  };

  return out;
}

module.exports = parseOne;
