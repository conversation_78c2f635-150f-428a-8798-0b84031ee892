<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆借用管理系统 - 管理员后台</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2c3e50">
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="app-title">🛠️ 管理员后台</h1>
                <div class="user-info">
                    <span class="user-name" id="userName">管理员</span>
                    <span class="user-role">系统管理员</span>
                </div>
            </div>
            <div class="header-right">
                <button class="user-btn" onclick="window.location.href='/dashboard.html'">用户界面</button>
                <button class="logout-btn" id="logoutBtn">退出登录</button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 统计卡片 -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card clickable" onclick="adminManager.showStatsDetails('vehicles')">
                    <div class="stat-icon">🚗</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalVehicles">-</div>
                        <div class="stat-label">总车辆数</div>
                    </div>
                    <div class="stat-arrow">👁️</div>
                </div>
                <div class="stat-card clickable" onclick="adminManager.showStatsDetails('available')">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number" id="availableVehicles">-</div>
                        <div class="stat-label">当前可用车辆数</div>
                    </div>
                    <div class="stat-arrow">👁️</div>
                </div>
                <div class="stat-card clickable" onclick="adminManager.showStatsDetails('current')">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <div class="stat-number" id="currentBorrowings">-</div>
                        <div class="stat-label">当前借用</div>
                    </div>
                    <div class="stat-arrow">👁️</div>
                </div>
                <div class="stat-card clickable" onclick="adminManager.showStatsDetails('overdue')">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-content">
                        <div class="stat-number" id="overdueCount">-</div>
                        <div class="stat-label">逾期记录</div>
                    </div>
                    <div class="stat-arrow">👁️</div>
                </div>
                <div class="stat-card clickable" onclick="adminManager.showStatsDetails('total')">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalRecords">-</div>
                        <div class="stat-label">总借用记录</div>
                    </div>
                    <div class="stat-arrow">👁️</div>
                </div>
                <div class="stat-card clickable" onclick="adminManager.showPendingReturns()">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <div class="stat-number" id="pendingReturns">-</div>
                        <div class="stat-label">待审批归还</div>
                    </div>
                    <div class="stat-arrow">👁️</div>
                </div>
            </div>
        </section>

        <!-- 导航标签 -->
        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="current">当前借用</button>
            <button class="tab-btn" data-tab="overdue">逾期管理</button>
            <button class="tab-btn" data-tab="records">所有记录</button>
            <button class="tab-btn" data-tab="reports">报告导出</button>
        </nav>

        <!-- 当前借用页面 -->
        <section id="currentTab" class="tab-content active">
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📋 当前借用状态</h2>
                    <button class="refresh-btn" id="refreshCurrent">刷新</button>
                </div>
                
                <div id="currentBorrowingsList" class="borrowings-list">
                    <div class="loading-message">正在加载当前借用记录...</div>
                </div>
            </div>
        </section>

        <!-- 逾期管理页面 -->
        <section id="overdueTab" class="tab-content">
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">⚠️ 逾期管理</h2>
                    <div class="header-actions">
                        <button class="action-btn" id="generateOverdueReport">生成逾期报告</button>
                        <button class="refresh-btn" id="refreshOverdue">刷新</button>
                    </div>
                </div>
                
                <div class="overdue-stats" id="overdueStats">
                    <div class="stat-item">
                        <span class="stat-label">总逾期:</span>
                        <span class="stat-value" id="totalOverdue">0</span>
                    </div>
                    <div class="stat-item critical">
                        <span class="stat-label">严重逾期(>7天):</span>
                        <span class="stat-value" id="criticalOverdue">0</span>
                    </div>
                    <div class="stat-item warning">
                        <span class="stat-label">警告逾期(3-7天):</span>
                        <span class="stat-value" id="warningOverdue">0</span>
                    </div>
                    <div class="stat-item light">
                        <span class="stat-label">轻度逾期(1-2天):</span>
                        <span class="stat-value" id="lightOverdue">0</span>
                    </div>
                    <div class="stat-item today">
                        <span class="stat-label">当天逾期:</span>
                        <span class="stat-value" id="todayOverdue">0</span>
                    </div>
                </div>

                <div id="overdueList" class="borrowings-list">
                    <div class="loading-message">正在加载逾期记录...</div>
                </div>
            </div>
        </section>

        <!-- 所有记录页面 -->
        <section id="recordsTab" class="tab-content">
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📊 所有借用记录</h2>
                    <div class="header-actions">
                        <button class="refresh-btn" id="refreshRecords">刷新</button>
                        <button class="filter-toggle-btn" id="toggleFilters">🔍 筛选</button>
                    </div>
                </div>

                <!-- 筛选面板 -->
                <div class="filters-panel" id="filtersPanel" style="display: none;">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="statusFilter">状态筛选</label>
                            <select id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="borrowed">借用中</option>
                                <option value="returned">已归还</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="vehicleTypeFilter">车辆类型</label>
                            <select id="vehicleTypeFilter">
                                <option value="">全部车辆</option>
                                <!-- 动态加载车辆选项 -->
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="borrowerFilter">借用人</label>
                            <select id="borrowerFilter">
                                <option value="">全部借用人</option>
                                <!-- 动态加载借用人选项 -->
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="dateRangeFilter">借用日期</label>
                            <select id="dateRangeFilter">
                                <option value="">全部日期</option>
                                <option value="today">今天</option>
                                <option value="3days">最近3天</option>
                                <option value="7days">最近7天</option>
                                <option value="30days">最近30天</option>
                                <option value="thisMonth">本月</option>
                                <option value="lastMonth">上月</option>
                                <option value="custom">自定义日期</option>
                            </select>
                        </div>

                        <div class="filter-group custom-date-group" id="customDateGroup" style="display: none;">
                            <label for="startDate">开始日期</label>
                            <input type="date" id="startDate">
                        </div>

                        <div class="filter-group custom-date-group" id="customDateGroup2" style="display: none;">
                            <label for="endDate">结束日期</label>
                            <input type="date" id="endDate">
                        </div>

                        <div class="filter-actions">
                            <button class="apply-filters-btn" id="applyFilters">应用筛选</button>
                            <button class="clear-filters-btn" id="clearFilters">清除筛选</button>
                        </div>
                    </div>
                </div>
                
                <div id="allRecordsList" class="borrowings-list">
                    <div class="loading-message">正在加载所有记录...</div>
                </div>
            </div>
        </section>

        <!-- 报告导出页面 -->
        <section id="reportsTab" class="tab-content">
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📈 报告导出</h2>
                </div>
                
                <div class="export-filters">
                    <h3>📊 导出筛选条件</h3>
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="exportVehicleType">车辆类型:</label>
                            <select id="exportVehicleType">
                                <option value="all">全部车辆</option>
                                <option value="广汽">广汽 T9M (临牌-黑色)</option>
                                <option value="高合">高合 VX1 (沪AH7673-白色)</option>
                                <option value="智己">智己 LS7展车 (沪ACF9608-白色)</option>
                                <option value="上汽">上汽 飞凡 (全部)</option>
                                <option value="沪BDR6188">上汽 飞凡 (沪BDR6188-灰色)</option>
                                <option value="沪ACE6153">上汽 飞凡 (沪ACE6153-黑色)</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="exportYear">年份:</label>
                            <select id="exportYear">
                                <option value="">全部年份</option>
                                <option value="2025">2025年</option>
                                <option value="2024">2024年</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="exportMonth">月份:</label>
                            <select id="exportMonth">
                                <option value="">全部月份</option>
                                <option value="1">1月</option>
                                <option value="2">2月</option>
                                <option value="3">3月</option>
                                <option value="4">4月</option>
                                <option value="5">5月</option>
                                <option value="6">6月</option>
                                <option value="7">7月</option>
                                <option value="8">8月</option>
                                <option value="9">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="reports-grid">
                    <div class="report-card">
                        <div class="report-icon">⚠️</div>
                        <div class="report-content">
                            <h3>逾期报告</h3>
                            <p>导出当前所有逾期记录的详细Excel报告</p>
                            <button class="export-btn" id="exportOverdueReport">📥 导出逾期报告</button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">📊</div>
                        <div class="report-content">
                            <h3>借用记录</h3>
                            <p>根据筛选条件导出借用记录，包含图片和视频</p>
                            <button class="export-btn" id="exportAllRecords">📥 导出借用记录</button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">🚗</div>
                        <div class="report-content">
                            <h3>车辆统计</h3>
                            <p>分析各车辆的使用频率和状态</p>
                            <button class="export-btn" id="exportVehicleStats">📥 导出车辆统计</button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">👥</div>
                        <div class="report-content">
                            <h3>用户统计</h3>
                            <p>统计各用户的借用频率和行为</p>
                            <button class="export-btn" id="exportUserStats">📥 导出用户统计</button>
                        </div>
                    </div>
                </div>

                <div class="network-info" style="display: none;">
                    <h3>📁 数据存储位置</h3>
                    <div class="storage-info" id="storageInfo">
                        <div class="storage-item">
                            <span class="storage-label">当前环境:</span>
                            <span class="storage-value" id="networkType">检测中...</span>
                        </div>
                        <div class="storage-item">
                            <span class="storage-label">数据路径:</span>
                            <span class="storage-value" id="dataPath">检测中...</span>
                        </div>
                        <div class="storage-item">
                            <span class="storage-label">报告位置:</span>
                            <span class="storage-value" id="reportsPath">检测中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 统计详情模态框 -->
    <div id="statsModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="statsModalTitle">统计详情</h3>
                <button class="close-btn" id="closeStatsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="statsModalContent" class="stats-modal-content">
                    <div class="loading-message">正在加载详情...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审批模态框 -->
    <div id="approvalModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>归还申请审批</h3>
                <button class="close-btn" id="closeApprovalModal" onclick="adminManager.closeApprovalModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="approvalContent"></div>
                <div class="approval-actions">
                    <div class="approval-notes">
                        <label for="approvalNotes">审批备注：</label>
                        <textarea id="approvalNotes" placeholder="请填写审批意见（拒绝时必填）"></textarea>
                    </div>
                    <div class="approval-buttons">
                        <button class="btn-approve" onclick="adminManager.approveReturn(true)">✅ 同意归还</button>
                        <button class="btn-reject" onclick="adminManager.approveReturn(false)">❌ 拒绝归还</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageToast" class="message-toast">
        <div class="toast-content">
            <span class="toast-icon"></span>
            <span class="toast-text"></span>
        </div>
    </div>

    <!-- 网络状态指示器 (已隐藏) -->
    <div class="network-indicator" id="networkIndicator" style="display: none;">
        <div class="network-status">
            <span class="status-dot"></span>
            <span class="status-text">检测中...</span>
        </div>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>
