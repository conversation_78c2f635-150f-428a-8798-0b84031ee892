const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs-extra');
const TimezoneConfig = require('./timezone-config');

class Database {
    constructor(networkDetector) {
        this.networkDetector = networkDetector;
        this.db = null;
        this.timezoneConfig = new TimezoneConfig();
    }

    // 初始化数据库
    async initialize() {
        const dbPath = await this.networkDetector.getDatabasePath();
        await fs.ensureDir(dbPath);
        
        const dbFile = path.join(dbPath, 'vehicle_management.db');
        
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(dbFile, (err) => {
                if (err) {
                    console.error('❌ 数据库连接失败:', err.message);
                    reject(err);
                } else {
                    console.log('✅ 数据库连接成功:', dbFile);
                    this.createTables().then(resolve).catch(reject);
                }
            });
        });
    }

    // 创建数据表
    async createTables() {
        const tables = [
            // 车辆表
            `CREATE TABLE IF NOT EXISTS vehicles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number TEXT UNIQUE NOT NULL,
                model TEXT NOT NULL,
                brand TEXT NOT NULL,
                color TEXT,
                status TEXT DEFAULT 'available',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // 借用记录表
            `CREATE TABLE IF NOT EXISTS borrowings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id TEXT NOT NULL,
                employee_name TEXT NOT NULL,
                vehicle_id INTEGER NOT NULL,
                borrow_date DATETIME NOT NULL,
                return_date DATETIME,
                actual_return_date DATETIME,
                status TEXT DEFAULT 'borrowed',
                borrow_media_path TEXT,
                return_media_path TEXT,
                borrow_media_type TEXT,
                return_media_type TEXT,
                notes TEXT,
                return_status TEXT DEFAULT 'pending',
                return_approval_date DATETIME,
                return_approval_admin TEXT,
                return_approval_notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
            )`,
            
            // 系统日志表
            `CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                employee_id TEXT,
                details TEXT,
                ip_address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // 用户密码表
            `CREATE TABLE IF NOT EXISTS user_passwords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const sql of tables) {
            await this.runQuery(sql);
        }

        // 插入默认车辆数据
        await this.insertDefaultVehicles();

        // 升级数据库结构
        await this.upgradeDatabase();

        console.log('✅ 数据表创建完成');
    }

    // 插入默认车辆数据
    async insertDefaultVehicles() {
        const vehicles = [
            { plate: '临牌', model: 'T9M', brand: '广汽', color: '黑色' },
            { plate: '沪AH7673', model: 'VX1', brand: '高合', color: '白色' },
            { plate: '沪ACF9608', model: 'LS7展车', brand: '智己', color: '白色' },
            { plate: '沪BDR6188', model: '飞凡', brand: '上汽', color: '灰色' },
            { plate: '沪ACE6153', model: '飞凡', brand: '上汽', color: '黑色' }
        ];

        for (const vehicle of vehicles) {
            const sql = `INSERT OR IGNORE INTO vehicles (plate_number, model, brand, color)
                        VALUES (?, ?, ?, ?)`;
            await this.runQuery(sql, [vehicle.plate, vehicle.model, vehicle.brand, vehicle.color]);
        }
    }

    // 升级数据库结构
    async upgradeDatabase() {
        try {
            // 检查是否需要添加新字段
            const tableInfo = await this.allQuery("PRAGMA table_info(borrowings)");
            const columnNames = tableInfo.map(col => col.name);

            const newColumns = [
                { name: 'return_status', sql: 'ALTER TABLE borrowings ADD COLUMN return_status TEXT DEFAULT "pending"' },
                { name: 'return_approval_date', sql: 'ALTER TABLE borrowings ADD COLUMN return_approval_date DATETIME' },
                { name: 'return_approval_admin', sql: 'ALTER TABLE borrowings ADD COLUMN return_approval_admin TEXT' },
                { name: 'return_approval_notes', sql: 'ALTER TABLE borrowings ADD COLUMN return_approval_notes TEXT' },
                { name: 'safety_officer', sql: 'ALTER TABLE borrowings ADD COLUMN safety_officer TEXT' },
                { name: 'test_type', sql: 'ALTER TABLE borrowings ADD COLUMN test_type TEXT DEFAULT "static"' },
                { name: 'external_employee_id', sql: 'ALTER TABLE borrowings ADD COLUMN external_employee_id TEXT' },
                { name: 'external_employee_name', sql: 'ALTER TABLE borrowings ADD COLUMN external_employee_name TEXT' }
            ];

            for (const column of newColumns) {
                if (!columnNames.includes(column.name)) {
                    await this.runQuery(column.sql);
                    console.log(`✅ 添加字段: ${column.name}`);
                }
            }
        } catch (error) {
            console.error('❌ 数据库升级失败:', error);
        }
    }

    // 执行SQL查询
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('❌ SQL执行失败:', err.message);
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    // 查询数据
    getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // 查询多条数据
    allQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // 获取可用车辆
    async getAvailableVehicles() {
        const sql = `SELECT * FROM vehicles WHERE status = 'available' ORDER BY brand, model`;
        return await this.allQuery(sql);
    }

    // 获取所有车辆
    async getAllVehicles() {
        const sql = `SELECT * FROM vehicles ORDER BY brand, model`;
        return await this.allQuery(sql);
    }

    // 创建借用记录
    async createBorrowing(data) {
        const sql = `INSERT INTO borrowings
                    (employee_id, employee_name, vehicle_id, borrow_date, return_date,
                     borrow_media_path, borrow_media_type, notes, safety_officer, test_type,
                     external_employee_id, external_employee_name)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        const params = [
            data.employee_id,
            data.employee_name,
            data.vehicle_id,
            data.borrow_date,
            data.return_date,
            data.borrow_media_path,
            data.borrow_media_type,
            data.notes,
            data.safety_officer || null,
            data.test_type || 'static',
            data.external_employee_id || null,
            data.external_employee_name || null
        ];

        const result = await this.runQuery(sql, params);
        
        // 更新车辆状态
        await this.runQuery('UPDATE vehicles SET status = ? WHERE id = ?', ['borrowed', data.vehicle_id]);
        
        return result;
    }

    // 归还车辆
    async returnVehicle(borrowingId, returnData) {
        const sql = `UPDATE borrowings SET 
                    actual_return_date = ?, 
                    return_media_path = ?, 
                    return_media_type = ?, 
                    status = 'returned' 
                    WHERE id = ?`;
        
        const params = [
            returnData.actual_return_date,
            returnData.return_media_path,
            returnData.return_media_type,
            borrowingId
        ];

        await this.runQuery(sql, params);
        
        // 获取车辆ID并更新状态
        const borrowing = await this.getQuery('SELECT vehicle_id FROM borrowings WHERE id = ?', [borrowingId]);
        if (borrowing) {
            await this.runQuery('UPDATE vehicles SET status = ? WHERE id = ?', ['available', borrowing.vehicle_id]);
        }
    }

    // 获取用户借用记录
    async getUserBorrowings(employeeId) {
        const sql = `SELECT b.*, v.plate_number, v.model, v.brand, v.color 
                    FROM borrowings b 
                    JOIN vehicles v ON b.vehicle_id = v.id 
                    WHERE b.employee_id = ? 
                    ORDER BY b.created_at DESC`;
        return await this.allQuery(sql, [employeeId]);
    }

    // 获取当前借用状态
    async getCurrentBorrowings() {
        const sql = `SELECT b.*, v.plate_number, v.model, v.brand, v.color 
                    FROM borrowings b 
                    JOIN vehicles v ON b.vehicle_id = v.id 
                    WHERE b.status = 'borrowed' 
                    ORDER BY b.borrow_date`;
        return await this.allQuery(sql);
    }

    // 获取逾期记录
    async getOverdueBorrowings() {
        // 使用时区配置确保内网外网一致性
        const timezoneOffset = this.timezoneConfig.getDatabaseTimezoneOffset();
        const sql = `SELECT b.*, v.plate_number, v.model, v.brand, v.color
                    FROM borrowings b
                    JOIN vehicles v ON b.vehicle_id = v.id
                    WHERE b.status = 'borrowed'
                    AND datetime(b.return_date) < datetime('now', '${timezoneOffset}')
                    ORDER BY b.return_date`;
        return await this.allQuery(sql);
    }

    // 获取所有借用记录
    async getAllBorrowings() {
        const sql = `SELECT b.*, v.plate_number, v.model, v.brand, v.color 
                    FROM borrowings b 
                    JOIN vehicles v ON b.vehicle_id = v.id 
                    ORDER BY b.created_at DESC`;
        return await this.allQuery(sql);
    }

    // 记录系统日志
    async logAction(action, employeeId, details, ipAddress) {
        const sql = `INSERT INTO system_logs (action, employee_id, details, ip_address)
                    VALUES (?, ?, ?, ?)`;
        await this.runQuery(sql, [action, employeeId, details, ipAddress]);
    }

    // 检查用户是否已设置密码
    async hasPassword(employeeId) {
        const sql = `SELECT id FROM user_passwords WHERE employee_id = ?`;
        const result = await this.getQuery(sql, [employeeId]);
        return !!result;
    }

    // 设置用户密码
    async setPassword(employeeId, passwordHash) {
        const sql = `INSERT OR REPLACE INTO user_passwords (employee_id, password_hash, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)`;
        await this.runQuery(sql, [employeeId, passwordHash]);
    }

    // 验证用户密码
    async verifyPassword(employeeId, passwordHash) {
        const sql = `SELECT password_hash FROM user_passwords WHERE employee_id = ?`;
        const result = await this.getQuery(sql, [employeeId]);
        return result && result.password_hash === passwordHash;
    }

    // 重置用户密码
    async resetPassword(employeeId, newPasswordHash) {
        const sql = `UPDATE user_passwords SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE employee_id = ?`;
        await this.runQuery(sql, [newPasswordHash, employeeId]);
    }

    // 内网部署优化 - 验证数据完整性
    async validateDataIntegrityForInternalDeployment() {
        console.log('🔍 验证内网部署数据完整性...');

        try {
            const results = {
                vehicles: await this.countRecords('vehicles'),
                borrowings: await this.countRecords('borrowings'),
                passwords: await this.countRecords('user_passwords'),
                logs: await this.countRecords('system_logs'),
                timezone: this.timezoneConfig.getTimezoneInfo()
            };

            console.log('📊 数据完整性验证结果:', results);

            // 验证逾期查询是否正常工作
            const overdueRecords = await this.getOverdueBorrowings();
            results.overdueRecords = overdueRecords.length;

            console.log(`✅ 找到 ${overdueRecords.length} 条逾期记录`);

            return results;

        } catch (error) {
            console.error('❌ 数据完整性验证失败:', error);
            throw error;
        }
    }

    // 统计表记录数
    async countRecords(tableName) {
        const sql = `SELECT COUNT(*) as count FROM ${tableName}`;
        const result = await this.getQuery(sql);
        return result ? result.count : 0;
    }

    // 内网部署时的时间数据修复
    async fixTimezoneDataForInternalDeployment() {
        console.log('🔧 修复内网部署时间数据...');

        try {
            // 检查是否需要时间数据修复
            const sampleBorrowing = await this.getQuery('SELECT borrow_date FROM borrowings LIMIT 1');

            if (sampleBorrowing) {
                const isValidTime = this.timezoneConfig.isValidDateTime(sampleBorrowing.borrow_date);

                if (!isValidTime) {
                    console.log('⚠️ 发现时间格式问题，开始修复...');

                    // 这里可以添加具体的时间格式修复逻辑
                    // 根据实际情况调整

                    console.log('✅ 时间数据修复完成');
                } else {
                    console.log('✅ 时间数据格式正常');
                }
            }

        } catch (error) {
            console.error('❌ 时间数据修复失败:', error);
            throw error;
        }
    }

    // 提交还车申请
    async submitReturnRequest(borrowingId, returnData) {
        const sql = `UPDATE borrowings SET
                    actual_return_date = ?,
                    return_media_path = ?,
                    return_media_type = ?,
                    return_notes = ?,
                    return_status = ?
                    WHERE id = ?`;

        const params = [
            returnData.actual_return_date,
            returnData.return_media_path,
            returnData.return_media_type,
            returnData.return_notes,
            returnData.return_status,
            borrowingId
        ];

        return await this.runQuery(sql, params);
    }

    // 获取待审批的还车申请
    async getPendingReturnRequests() {
        const sql = `SELECT b.*, v.plate_number, v.model, v.brand, v.color
                    FROM borrowings b
                    JOIN vehicles v ON b.vehicle_id = v.id
                    WHERE b.return_status = 'pending'
                    ORDER BY b.actual_return_date DESC`;
        return await this.allQuery(sql);
    }

    // 同意还车申请
    async approveReturn(borrowingId, approvalData) {
        const sql = `UPDATE borrowings SET
                    status = 'returned',
                    return_status = ?,
                    return_approval_date = ?,
                    return_approval_admin = ?,
                    return_approval_notes = ?
                    WHERE id = ?`;

        const params = [
            approvalData.return_status,
            approvalData.return_approval_date,
            approvalData.return_approval_admin,
            approvalData.return_approval_notes,
            borrowingId
        ];

        await this.runQuery(sql, params);

        // 更新车辆状态为可用
        const borrowing = await this.getQuery('SELECT vehicle_id FROM borrowings WHERE id = ?', [borrowingId]);
        if (borrowing) {
            await this.runQuery('UPDATE vehicles SET status = ? WHERE id = ?', ['available', borrowing.vehicle_id]);
        }
    }

    // 拒绝还车申请
    async rejectReturn(borrowingId, approvalData) {
        const sql = `UPDATE borrowings SET
                    return_status = ?,
                    return_approval_date = ?,
                    return_approval_admin = ?,
                    return_approval_notes = ?
                    WHERE id = ?`;

        const params = [
            approvalData.return_status,
            approvalData.return_approval_date,
            approvalData.return_approval_admin,
            approvalData.return_approval_notes,
            borrowingId
        ];

        return await this.runQuery(sql, params);
    }

    // 关闭数据库连接
    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('❌ 数据库关闭失败:', err.message);
                } else {
                    console.log('✅ 数据库连接已关闭');
                }
            });
        }
    }
}

module.exports = Database;
