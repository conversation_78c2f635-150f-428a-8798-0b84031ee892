{"version": 3, "file": "ColumnParser.js", "sourceRoot": "", "sources": ["../../../../src/parser/column/ColumnParser.ts"], "names": [], "mappings": ";;;AACA,mEAAgE;AAChE,6DAA0D;AAE1D,oCAAiC;AAEjC,MAAa,YAAY;IAOrB,YAAmB,aAA4B;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,aAAa,CAAC,CAAC;QAChE,IAAI,CAAC,qBAAqB,GAAG,IAAI,6CAAqB,CAAC,aAAa,CAAC,CAAC;IAC1E,CAAC;IAEM,KAAK,CAAC,OAAgB;QACzB,MAAM,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;QACtC,IAAI,iBAAiB,KAAK,IAAI,IAAI,aAAK,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;YACzF,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACjD;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;CACJ;AArBD,oCAqBC"}