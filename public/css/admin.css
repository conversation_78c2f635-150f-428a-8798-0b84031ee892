/* 管理员界面样式 */
/* 继承基础样式 */
@import url('dashboard.css');

/* 重写特定样式 */
.header {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.app-title {
    color: white;
}

.user-role {
    font-size: 12px;
    opacity: 0.9;
    color: #ecf0f1;
}

.user-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.user-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 统计卡片区域 */
.stats-section {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-card.clickable {
    cursor: pointer;
}

.stat-card.clickable:hover {
    background: rgba(102, 126, 234, 0.05);
    border-color: rgba(102, 126, 234, 0.3);
}

.stat-arrow {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 16px;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.stat-card.clickable:hover .stat-arrow {
    opacity: 1;
    transform: scale(1.2);
}

.stat-icon {
    font-size: 48px;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.refresh-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

/* 逾期统计 */
.overdue-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(231, 76, 60, 0.05);
    border: 1px solid rgba(231, 76, 60, 0.2);
    border-radius: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-item.critical {
    border-left: 4px solid #e74c3c;
}

.stat-item.warning {
    border-left: 4px solid #f39c12;
}

.stat-item.light {
    border-left: 4px solid #3498db;
}

.stat-item.today {
    border-left: 4px solid #9b59b6;
}

.stat-item .stat-label {
    font-size: 14px;
    font-weight: 600;
    color: #34495e;
}

.stat-item .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.stat-item.critical .stat-value {
    color: #e74c3c;
}

.stat-item.warning .stat-value {
    color: #f39c12;
}

.stat-item.light .stat-value {
    color: #3498db;
}

.stat-item.today .stat-value {
    color: #9b59b6;
}

/* 借用记录列表 */
.borrowings-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.admin-borrowing-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.admin-borrowing-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.admin-borrowing-item.overdue {
    border-left: 4px solid #e74c3c;
    background: rgba(231, 76, 60, 0.02);
}

.admin-borrowing-item.critical-overdue {
    border-left: 4px solid #c0392b;
    background: rgba(192, 57, 43, 0.05);
}

.admin-borrowing-item.warning-overdue {
    border-left: 4px solid #f39c12;
    background: rgba(243, 156, 18, 0.03);
}

.admin-borrowing-item.light-overdue {
    border-left: 4px solid #3498db;
    background: rgba(52, 152, 219, 0.03);
}

.admin-borrowing-item.today-overdue {
    border-left: 4px solid #9b59b6;
    background: rgba(155, 89, 182, 0.03);
}

.borrowing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.borrowing-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.overdue-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.overdue-badge.critical {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.overdue-badge.warning {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
}

.overdue-badge.light {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.overdue-badge.today {
    background: rgba(155, 89, 182, 0.2);
    color: #9b59b6;
}

.borrowing-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    font-size: 14px;
    color: #7f8c8d;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-label {
    font-weight: 600;
    color: #34495e;
}

.detail-value {
    color: #2c3e50;
}

.detail-value.overdue {
    color: #e74c3c;
    font-weight: 600;
}

/* 导出筛选区域 */
.export-filters {
    background: rgba(52, 152, 219, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
}

.export-filters h3 {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 600;
    color: #34495e;
}

.filter-group select {
    padding: 10px 12px;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 报告导出区域 */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.report-card {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.report-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.report-content h3 {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.report-content p {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 20px;
    line-height: 1.5;
}

.export-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.export-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 网络信息 */
.network-info {
    background: rgba(52, 152, 219, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 12px;
    padding: 25px;
}

.network-info h3 {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.storage-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.storage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.storage-label {
    font-size: 14px;
    font-weight: 600;
    color: #34495e;
}

.storage-value {
    font-size: 14px;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    background: rgba(236, 240, 241, 0.5);
    padding: 4px 8px;
    border-radius: 4px;
}

/* 筛选器 */
#recordsFilter {
    padding: 8px 12px;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
}

/* 统计详情模态框 */
.modal-content.large {
    width: 95%;
    max-width: 1000px;
    max-height: 90vh;
}

.stats-modal-content {
    max-height: 70vh;
    overflow-y: auto;
}

.stats-detail-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.stats-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.detail-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.detail-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.detail-status.available {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.detail-status.borrowed {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
}

.detail-status.returned {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.detail-status.overdue {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-field {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-field-label {
    font-weight: 600;
    color: #34495e;
    font-size: 14px;
}

.detail-field-value {
    color: #2c3e50;
    font-size: 14px;
}

.media-preview {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.media-item {
    flex: 1;
    max-width: 200px;
}

.media-item h4 {
    font-size: 14px;
    font-weight: 600;
    color: #34495e;
    margin-bottom: 8px;
}

.media-item img,
.media-item video {
    width: 100%;
    max-height: 150px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.media-item img:hover,
.media-item video:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 多文件网格布局 */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.media-grid .media-item {
    position: relative;
    max-width: none;
    flex: none;
}

.media-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 5px 8px;
    font-size: 11px;
    text-align: center;
    border-radius: 0 0 8px 8px;
}

.no-media {
    color: #7f8c8d;
    font-style: italic;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-icon {
        font-size: 36px;
        width: 60px;
        height: 60px;
    }
    
    .stat-number {
        font-size: 28px;
    }
    
    .card-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: space-between;
    }
    
    .overdue-stats {
        grid-template-columns: 1fr;
    }
    
    .borrowing-details {
        grid-template-columns: 1fr;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .storage-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .storage-value {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .header-left,
    .header-right {
        justify-content: center;
    }
    
    .header-right {
        display: flex;
        gap: 10px;
    }
    
    .main-content {
        padding: 15px 10px;
    }
    
    .content-card {
        padding: 20px 15px;
    }
}

/* 审批相关样式 */
.pending-return-item {
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 16px;
    transition: all 0.3s ease;
}

.pending-return-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.pending-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.pending-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.approve-btn {
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.approve-btn:hover {
    background: #2980b9;
}

.pending-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.approval-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e8ed;
}

.approval-notes {
    margin-bottom: 15px;
}

.approval-notes label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.approval-notes textarea {
    width: 100%;
    min-height: 80px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    box-sizing: border-box;
}

.approval-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.btn-approve, .btn-reject {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-approve {
    background: #27ae60;
    color: white;
}

.btn-approve:hover {
    background: #229954;
}

.btn-reject {
    background: #e74c3c;
    color: white;
}

.btn-reject:hover {
    background: #c0392b;
}

.approval-details h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 18px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

/* 筛选面板样式 */
.filter-toggle-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.filter-toggle-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.filters-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 14px;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-actions {
    display: flex;
    gap: 10px;
    grid-column: 1 / -1;
    justify-content: flex-end;
    margin-top: 10px;
}

.apply-filters-btn,
.clear-filters-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.apply-filters-btn {
    background: #27ae60;
    color: white;
}

.apply-filters-btn:hover {
    background: #229954;
    transform: translateY(-1px);
}

.clear-filters-btn {
    background: #95a5a6;
    color: white;
}

.clear-filters-btn:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

.custom-date-group {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filters-grid {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        justify-content: center;
    }
}
