'use strict'

const fs = require('../fs')
const u = require('universalify').fromPromise

async function symlinkType (srcpath, type) {
  if (type) return type

  let stats
  try {
    stats = await fs.lstat(srcpath)
  } catch {
    return 'file'
  }

  return (stats && stats.isDirectory()) ? 'dir' : 'file'
}

function symlinkTypeSync (srcpath, type) {
  if (type) return type

  let stats
  try {
    stats = fs.lstatSync(srcpath)
  } catch {
    return 'file'
  }
  return (stats && stats.isDirectory()) ? 'dir' : 'file'
}

module.exports = {
  symlinkType: u(symlinkType),
  symlinkTypeSync
}
