<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            border: 1px solid #ddd;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .api-test {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .result-box {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
        .vehicle-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .vehicle-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #3498db;
        }
        .vehicle-plate {
            font-weight: bold;
            color: #e74c3c;
            font-size: 16px;
        }
        .vehicle-info {
            color: #2c3e50;
            margin: 5px 0;
        }
        .status-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-available {
            background: #d4edda;
            color: #27ae60;
        }
        .status-borrowed {
            background: #fff3cd;
            color: #f39c12;
        }
    </style>
</head>
<body>
    <h1>🚗 车辆API测试工具</h1>
    
    <div class="test-section">
        <h2 class="test-title">🔧 API测试</h2>
        <p>测试车辆相关的API端点，确认数据获取是否正常。</p>
        
        <div class="api-test">
            <h4>测试 /api/vehicles/all</h4>
            <button class="test-button" onclick="testVehiclesAll()">测试获取所有车辆</button>
            <div id="vehiclesAllResult" class="result-box" style="display: none;"></div>
        </div>
        
        <div class="api-test">
            <h4>测试 /api/vehicles</h4>
            <button class="test-button" onclick="testVehiclesAvailable()">测试获取可用车辆</button>
            <div id="vehiclesAvailableResult" class="result-box" style="display: none;"></div>
        </div>
        
        <div class="api-test">
            <h4>测试 /api/vehicles/status</h4>
            <button class="test-button" onclick="testVehiclesStatus()">测试获取车辆状态</button>
            <div id="vehiclesStatusResult" class="result-box" style="display: none;"></div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 车辆列表展示</h2>
        <button class="test-button" onclick="displayVehiclesList()">显示车辆列表</button>
        <div id="vehiclesList"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔍 筛选选项模拟</h2>
        <button class="test-button" onclick="simulateFilterOptions()">模拟筛选选项生成</button>
        <div id="filterSimulation"></div>
    </div>

    <script>
        async function testVehiclesAll() {
            const resultDiv = document.getElementById('vehiclesAllResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试 /api/vehicles/all...';
            
            try {
                const response = await fetch('/api/vehicles/all');
                const data = await response.json();
                
                resultDiv.textContent = `状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                
                if (data.success && data.vehicles) {
                    resultDiv.textContent += `\n\n✅ 成功获取 ${data.vehicles.length} 辆车`;
                } else {
                    resultDiv.textContent += '\n\n❌ 获取失败或数据为空';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testVehiclesAvailable() {
            const resultDiv = document.getElementById('vehiclesAvailableResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试 /api/vehicles...';
            
            try {
                const response = await fetch('/api/vehicles');
                const data = await response.json();
                
                resultDiv.textContent = `状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                
                if (data.success && data.vehicles) {
                    resultDiv.textContent += `\n\n✅ 成功获取 ${data.vehicles.length} 辆可用车`;
                } else {
                    resultDiv.textContent += '\n\n❌ 获取失败或数据为空';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testVehiclesStatus() {
            const resultDiv = document.getElementById('vehiclesStatusResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试 /api/vehicles/status...';
            
            try {
                const response = await fetch('/api/vehicles/status');
                const data = await response.json();
                
                resultDiv.textContent = `状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                
                if (data.success && data.vehicles) {
                    resultDiv.textContent += `\n\n✅ 成功获取 ${data.vehicles.length} 辆车的状态`;
                } else {
                    resultDiv.textContent += '\n\n❌ 获取失败或数据为空';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function displayVehiclesList() {
            const listDiv = document.getElementById('vehiclesList');
            listDiv.innerHTML = '<p>正在加载车辆列表...</p>';
            
            try {
                const response = await fetch('/api/vehicles/all');
                const data = await response.json();
                
                if (data.success && data.vehicles && data.vehicles.length > 0) {
                    const vehiclesHtml = data.vehicles.map(vehicle => `
                        <div class="vehicle-card">
                            <div class="vehicle-plate">${vehicle.plate_number}</div>
                            <div class="vehicle-info">${vehicle.brand} ${vehicle.model}</div>
                            <div class="vehicle-info">颜色: ${vehicle.color}</div>
                            <div class="vehicle-info">
                                状态: <span class="status-indicator status-${vehicle.status}">${vehicle.status === 'available' ? '可用' : '借用中'}</span>
                            </div>
                        </div>
                    `).join('');
                    
                    listDiv.innerHTML = `
                        <p class="success">✅ 找到 ${data.vehicles.length} 辆车</p>
                        <div class="vehicle-list">${vehiclesHtml}</div>
                    `;
                } else {
                    listDiv.innerHTML = '<p class="error">❌ 未找到车辆数据</p>';
                }
            } catch (error) {
                listDiv.innerHTML = `<p class="error">❌ 加载失败: ${error.message}</p>`;
            }
        }

        async function simulateFilterOptions() {
            const simDiv = document.getElementById('filterSimulation');
            simDiv.innerHTML = '<p>正在模拟筛选选项生成...</p>';
            
            try {
                const response = await fetch('/api/vehicles/all');
                const data = await response.json();
                
                if (data.success && data.vehicles && data.vehicles.length > 0) {
                    // 模拟筛选选项生成逻辑
                    const vehicleTypes = data.vehicles.map(vehicle => {
                        const vehicleInfo = `${vehicle.plate_number} - ${vehicle.brand} ${vehicle.model}`;
                        return {
                            value: vehicle.plate_number,
                            display: vehicleInfo
                        };
                    }).sort((a, b) => a.display.localeCompare(b.display));
                    
                    const optionsHtml = vehicleTypes.map(vehicle => 
                        `<option value="${vehicle.value}">${vehicle.display}</option>`
                    ).join('');
                    
                    simDiv.innerHTML = `
                        <p class="success">✅ 生成 ${vehicleTypes.length} 个筛选选项</p>
                        <h4>模拟的筛选下拉框:</h4>
                        <select style="width: 100%; padding: 8px; margin: 10px 0;">
                            <option value="">全部车辆</option>
                            ${optionsHtml}
                        </select>
                        <h4>选项详情:</h4>
                        <div class="result-box">${JSON.stringify(vehicleTypes, null, 2)}</div>
                    `;
                } else {
                    simDiv.innerHTML = '<p class="error">❌ 无法生成筛选选项，车辆数据为空</p>';
                }
            } catch (error) {
                simDiv.innerHTML = `<p class="error">❌ 模拟失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
