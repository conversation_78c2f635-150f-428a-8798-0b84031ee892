<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .preview {
            margin-top: 15px;
            display: none;
        }
        .preview.show {
            display: block;
        }
        .preview img, .preview video {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
        }
        .file-info {
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 6px;
            border-left: 3px solid #3498db;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ef5350;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
    </style>
</head>
<body>
    <h1>视频上传崩溃修复测试</h1>
    
    <div class="test-section">
        <h2>测试1: 使用旧方法 (readAsDataURL) - 可能崩溃</h2>
        <input type="file" id="oldMethod" accept="image/*,video/*">
        <div id="oldPreview" class="preview"></div>
        <div id="oldMessage"></div>
    </div>

    <div class="test-section">
        <h2>测试2: 使用新方法 (createObjectURL) - 已修复</h2>
        <input type="file" id="newMethod" accept="image/*,video/*">
        <div id="newPreview" class="preview"></div>
        <div id="newMessage"></div>
    </div>

    <script>
        // 旧方法 - 使用 readAsDataURL (可能导致崩溃)
        document.getElementById('oldMethod').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('oldPreview');
            const messageDiv = document.getElementById('oldMessage');
            
            if (!file) {
                preview.classList.remove('show');
                return;
            }

            // 检查文件大小
            const maxSize = 200 * 1024 * 1024; // 200MB
            if (file.size > maxSize) {
                messageDiv.innerHTML = `<div class="message error">文件大小超过200MB限制，当前文件大小：${(file.size / 1024 / 1024).toFixed(2)}MB</div>`;
                preview.classList.remove('show');
                return;
            }

            messageDiv.innerHTML = `<div class="message">正在使用旧方法加载文件...</div>`;

            const reader = new FileReader();
            reader.onload = function(e) {
                const isImage = file.type.startsWith('image/');
                const isVideo = file.type.startsWith('video/');

                if (isImage) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="预览图片">`;
                } else if (isVideo) {
                    preview.innerHTML = `
                        <video src="${e.target.result}" controls>
                            您的浏览器不支持视频播放
                        </video>
                        <div class="file-info">
                            <small>文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB (使用readAsDataURL)</small>
                        </div>
                    `;
                }

                preview.classList.add('show');
                messageDiv.innerHTML = `<div class="message success">旧方法加载完成</div>`;
            };

            reader.onerror = function() {
                messageDiv.innerHTML = `<div class="message error">旧方法加载失败</div>`;
            };

            reader.readAsDataURL(file);
        });

        // 新方法 - 使用 createObjectURL (已修复)
        let objectURLs = {};

        document.getElementById('newMethod').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('newPreview');
            const messageDiv = document.getElementById('newMessage');
            const previewId = 'newPreview';
            
            if (!file) {
                preview.classList.remove('show');
                cleanupObjectURL(previewId);
                return;
            }

            // 检查文件大小
            const maxSize = 200 * 1024 * 1024; // 200MB
            if (file.size > maxSize) {
                messageDiv.innerHTML = `<div class="message error">文件大小超过200MB限制，当前文件大小：${(file.size / 1024 / 1024).toFixed(2)}MB</div>`;
                preview.classList.remove('show');
                return;
            }

            const isImage = file.type.startsWith('image/');
            const isVideo = file.type.startsWith('video/');

            if (!isImage && !isVideo) {
                messageDiv.innerHTML = `<div class="message error">请选择图片或视频文件</div>`;
                preview.classList.remove('show');
                return;
            }

            messageDiv.innerHTML = `<div class="message">正在使用新方法加载文件...</div>`;

            // 清理之前的对象URL
            cleanupObjectURL(previewId);

            // 使用 createObjectURL 替代 readAsDataURL，避免内存溢出
            const objectURL = URL.createObjectURL(file);
            objectURLs[previewId] = objectURL;

            if (isImage) {
                preview.innerHTML = `<img src="${objectURL}" alt="预览图片" style="max-width: 100%; max-height: 300px;">`;
            } else if (isVideo) {
                preview.innerHTML = `
                    <video src="${objectURL}" controls style="max-width: 100%; max-height: 300px;" preload="metadata">
                        您的浏览器不支持视频播放
                    </video>
                    <div class="file-info">
                        <small>文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB (使用createObjectURL)</small>
                    </div>
                `;
            }

            preview.classList.add('show');
            messageDiv.innerHTML = `<div class="message success">新方法加载完成 - 内存使用优化</div>`;
        });

        function cleanupObjectURL(previewId) {
            if (objectURLs && objectURLs[previewId]) {
                URL.revokeObjectURL(objectURLs[previewId]);
                delete objectURLs[previewId];
            }
        }

        // 页面卸载时清理所有对象URL
        window.addEventListener('beforeunload', function() {
            Object.values(objectURLs).forEach(url => {
                URL.revokeObjectURL(url);
            });
            objectURLs = {};
        });
    </script>
</body>
</html>
