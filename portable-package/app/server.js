const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const cors = require('cors');
const bodyParser = require('body-parser');
const moment = require('moment');
const crypto = require('crypto');

// 导入自定义模块
const NetworkDetector = require('./network-detector');
const Database = require('./database');
const OverdueChecker = require('./overdue-checker');
const MonthlyReportGenerator = require('./monthly-report-generator');

// 导入员工数据
const employeesData = require('./employees.json');

const app = express();
const PORT = process.env.PORT || 3000;

// 初始化模块
const networkDetector = new NetworkDetector();
const database = new Database(networkDetector);
let overdueChecker;
let monthlyReportGenerator;

// 中间件配置
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// 配置文件上传
const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
        try {
            const uploadPath = await networkDetector.getUploadPath();

            // 直接使用uploads目录，不创建子目录
            await fs.ensureDir(uploadPath);

            cb(null, uploadPath);
        } catch (error) {
            console.error('文件存储路径错误:', error);
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        // 使用时间戳和随机数生成唯一文件名，避免冲突
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const ext = path.extname(file.originalname);
        const mediaType = file.mimetype.startsWith('image/') ? '图片' : '视频';
        const filename = `${mediaType}_${timestamp}_${random}${ext}`;
        cb(null, filename);
    }
});

const upload = multer({
    storage: storage,
    // 取消文件大小限制
    // limits: {
    //     fileSize: 50 * 1024 * 1024 // 50MB
    // },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|mp4|mov|avi|wmv/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('只允许上传图片和视频文件'));
        }
    }
});

// API路由

// 简单密码哈希函数
function hashPassword(password) {
    return crypto.createHash('sha256').update(password).digest('hex');
}

// 员工登录验证
app.post('/api/login', async (req, res) => {
    try {
        const { employee_id, pinyin, password } = req.body;

        if (!employee_id || !pinyin) {
            return res.status(400).json({
                success: false,
                message: '请输入工号和姓名拼音'
            });
        }

        // 检查是否是外部门人员（工号001）
        if (employee_id.toUpperCase() === '001') {
            // 外部门人员登录，无需验证员工名单和拼音
            return res.json({
                success: true,
                message: '外部门人员登录成功',
                user: {
                    id: '001',
                    name: '外部门人员',
                    pinyin: pinyin || '',  // 拼音可为空
                    isAdmin: false,
                    isExternal: true
                }
            });
        }

        // 查找员工
        const employee = employeesData.employees.find(emp =>
            emp.id.toUpperCase() === employee_id.toUpperCase()
        );

        if (!employee) {
            return res.status(401).json({
                success: false,
                message: '工号不存在'
            });
        }

        // 验证拼音
        if (employee.pinyin.toLowerCase() !== pinyin.toLowerCase().trim()) {
            return res.status(401).json({
                success: false,
                message: '姓名拼音不正确'
            });
        }

        const isAdmin = employee_id === 'admin' || employee_id === '000000';

        // 管理员无需密码
        if (!isAdmin) {
            // 检查是否已设置密码
            const hasPassword = await database.hasPassword(employee_id);

            if (!hasPassword) {
                return res.json({
                    success: false,
                    needSetPassword: true,
                    message: '首次登录，请设置密码',
                    user: {
                        id: employee.id,
                        name: employee.name,
                        pinyin: employee.pinyin,
                        isAdmin: false
                    }
                });
            }

            // 验证密码
            if (!password) {
                return res.status(400).json({
                    success: false,
                    message: '请输入密码'
                });
            }

            const passwordHash = hashPassword(password);
            const isValidPassword = await database.verifyPassword(employee_id, passwordHash);

            if (!isValidPassword) {
                return res.status(401).json({
                    success: false,
                    message: '密码错误'
                });
            }
        }

        // 记录登录日志
        await database.logAction('LOGIN', employee_id, `用户登录: ${employee.name}`, req.ip);

        res.json({
            success: true,
            message: '登录成功',
            user: {
                id: employee.id,
                name: employee.name,
                pinyin: employee.pinyin,
                isAdmin: isAdmin
            }
        });

    } catch (error) {
        console.error('登录错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 设置密码
app.post('/api/set-password', async (req, res) => {
    try {
        const { employee_id, password, confirmPassword } = req.body;

        if (!employee_id || !password || !confirmPassword) {
            return res.status(400).json({
                success: false,
                message: '请填写所有必填项'
            });
        }

        if (password !== confirmPassword) {
            return res.status(400).json({
                success: false,
                message: '两次输入的密码不一致'
            });
        }

        if (password.length < 1) {
            return res.status(400).json({
                success: false,
                message: '密码不能为空'
            });
        }

        // 验证员工是否存在
        const employee = employeesData.employees.find(emp =>
            emp.id.toUpperCase() === employee_id.toUpperCase()
        );

        if (!employee) {
            return res.status(401).json({
                success: false,
                message: '员工不存在'
            });
        }

        const passwordHash = hashPassword(password);
        await database.setPassword(employee_id, passwordHash);

        // 记录日志
        await database.logAction('SET_PASSWORD', employee_id, `设置密码: ${employee.name}`, req.ip);

        res.json({
            success: true,
            message: '密码设置成功'
        });

    } catch (error) {
        console.error('设置密码错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 重置密码
app.post('/api/reset-password', async (req, res) => {
    try {
        const { employee_id, pinyin, newPassword, confirmPassword } = req.body;

        if (!employee_id || !pinyin || !newPassword || !confirmPassword) {
            return res.status(400).json({
                success: false,
                message: '请填写所有必填项'
            });
        }

        if (newPassword !== confirmPassword) {
            return res.status(400).json({
                success: false,
                message: '两次输入的密码不一致'
            });
        }

        // 验证员工信息
        const employee = employeesData.employees.find(emp =>
            emp.id.toUpperCase() === employee_id.toUpperCase()
        );

        if (!employee) {
            return res.status(401).json({
                success: false,
                message: '工号不存在'
            });
        }

        if (employee.pinyin.toLowerCase() !== pinyin.toLowerCase().trim()) {
            return res.status(401).json({
                success: false,
                message: '姓名拼音不正确'
            });
        }

        const passwordHash = hashPassword(newPassword);
        await database.resetPassword(employee_id, passwordHash);

        // 记录日志
        await database.logAction('RESET_PASSWORD', employee_id, `重置密码: ${employee.name}`, req.ip);

        res.json({
            success: true,
            message: '密码重置成功'
        });

    } catch (error) {
        console.error('重置密码错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 获取员工信息（用于前端验证）
app.get('/api/employee/:id', (req, res) => {
    const employeeId = req.params.id.toUpperCase();
    const employee = employeesData.employees.find(emp => emp.id === employeeId);
    
    if (employee) {
        res.json({
            success: true,
            employee: {
                id: employee.id,
                name: employee.name,
                pinyin: employee.pinyin
            }
        });
    } else {
        res.status(404).json({
            success: false,
            message: '员工不存在'
        });
    }
});

// 获取可用车辆
app.get('/api/vehicles', async (req, res) => {
    try {
        const vehicles = await database.getAvailableVehicles();
        res.json({
            success: true,
            vehicles
        });
    } catch (error) {
        console.error('获取车辆列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取车辆列表失败'
        });
    }
});

// 获取所有车辆（用于筛选）
app.get('/api/vehicles/all', async (req, res) => {
    try {
        const vehicles = await database.getAllVehicles();
        res.json({
            success: true,
            vehicles
        });
    } catch (error) {
        console.error('获取所有车辆错误:', error);
        res.status(500).json({
            success: false,
            message: '获取车辆列表失败'
        });
    }
});

// 获取车辆状态（包含借用者信息）
app.get('/api/vehicles/status', async (req, res) => {
    try {
        const sql = `
            SELECT
                v.*,
                b.employee_id as borrower_employee_id,
                b.employee_name as borrower_name,
                b.return_date as borrower_return_date,
                b.borrow_date as borrower_borrow_date
            FROM vehicles v
            LEFT JOIN borrowings b ON v.id = b.vehicle_id AND b.status = 'borrowed'
            ORDER BY v.id
        `;

        const vehiclesWithStatus = await database.allQuery(sql);

        const vehicles = vehiclesWithStatus.map(vehicle => {
            const result = {
                id: vehicle.id,
                plate_number: vehicle.plate_number,
                model: vehicle.model,
                brand: vehicle.brand,
                color: vehicle.color,
                status: vehicle.status
            };

            // 如果车辆被借用，添加借用者信息
            if (vehicle.status === 'borrowed' && vehicle.borrower_employee_id) {
                result.borrower = {
                    employee_id: vehicle.borrower_employee_id,
                    name: vehicle.borrower_name,
                    return_date: vehicle.borrower_return_date,
                    borrow_date: vehicle.borrower_borrow_date
                };
            }

            return result;
        });

        res.json({
            success: true,
            vehicles: vehicles
        });
    } catch (error) {
        console.error('获取车辆状态错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// 借车申请
app.post('/api/borrow', upload.array('borrowMedia', 10), async (req, res) => {
    try {
        const { employee_id, employee_name, vehicle_id, borrow_date, return_date, notes,
                test_type, safety_officer, external_employee_id, external_employee_name } = req.body;

        const borrowingData = {
            employee_id,
            employee_name,
            vehicle_id: parseInt(vehicle_id),
            borrow_date,
            return_date,
            notes,
            test_type: test_type || 'static',
            safety_officer,
            external_employee_id,
            external_employee_name
        };

        // 如果有上传媒体文件，则移动到正确位置
        if (req.files && req.files.length > 0) {
            const finalPaths = await moveMultipleFilesToCorrectLocation(req, 'borrow', {
                employee_id,
                employee_name,
                vehicle_id: parseInt(vehicle_id)
            });

            // 存储多文件路径（JSON格式）
            borrowingData.borrow_media_path = JSON.stringify(finalPaths.map(f => f.path));
            borrowingData.borrow_media_type = 'multiple'; // 标记为多文件
        }

        const result = await database.createBorrowing(borrowingData);
        
        // 记录日志
        await database.logAction('BORROW', employee_id, `借车: 车辆ID ${vehicle_id}`, req.ip);

        res.json({
            success: true,
            message: '借车申请成功',
            borrowing_id: result.id
        });

    } catch (error) {
        console.error('借车申请错误:', error);
        res.status(500).json({
            success: false,
            message: '借车申请失败'
        });
    }
});

// 获取用户借用记录
app.get('/api/user/:id/borrowings', async (req, res) => {
    try {
        const employeeId = req.params.id;
        const borrowings = await database.getUserBorrowings(employeeId);
        res.json({
            success: true,
            borrowings
        });
    } catch (error) {
        console.error('获取借用记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取借用记录失败'
        });
    }
});

// 还车
app.post('/api/return', upload.array('returnMedia', 10), async (req, res) => {
    try {
        const { borrowing_id } = req.body;

        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: '还车时必须上传图片或视频'
            });
        }

        // 移动多个文件到正确位置
        const finalPaths = await moveMultipleFilesToCorrectLocation(req, 'return', {
            borrowing_id,
            employee_id: req.body.employee_id,
            employee_name: req.body.employee_name
        });

        const returnData = {
            actual_return_date: moment().format('YYYY-MM-DD HH:mm:ss'),
            return_media_path: JSON.stringify(finalPaths.map(f => f.path)),
            return_media_type: 'multiple', // 标记为多文件
            return_notes: req.body.returnNotes || '', // 还车备注
            return_status: 'pending' // 设置为待审批状态
        };

        await database.submitReturnRequest(borrowing_id, returnData);

        // 记录日志
        await database.logAction('RETURN_REQUEST', req.body.employee_id, `提交还车申请: 借用ID ${borrowing_id}`, req.ip);

        res.json({
            success: true,
            message: '还车申请已提交，等待管理员审批'
        });

    } catch (error) {
        console.error('还车错误:', error);
        res.status(500).json({
            success: false,
            message: '还车失败'
        });
    }
});

// 获取待审批的还车申请
app.get('/api/admin/pending-returns', async (req, res) => {
    try {
        const pendingReturns = await database.getPendingReturnRequests();
        res.json({
            success: true,
            requests: pendingReturns
        });
    } catch (error) {
        console.error('获取待审批申请错误:', error);
        res.status(500).json({
            success: false,
            message: '获取失败'
        });
    }
});

// 管理员审批还车申请
app.post('/api/admin/approve-return', async (req, res) => {
    try {
        const { borrowing_id, approved, admin_id, notes } = req.body;

        if (!borrowing_id || approved === undefined) {
            return res.status(400).json({
                success: false,
                message: '缺少必要参数'
            });
        }

        const approvalData = {
            return_status: approved ? 'approved' : 'rejected',
            return_approval_date: moment().format('YYYY-MM-DD HH:mm:ss'),
            return_approval_admin: admin_id || 'admin',
            return_approval_notes: notes || ''
        };

        if (approved) {
            // 同意归还，更新车辆状态为可用
            await database.approveReturn(borrowing_id, approvalData);
        } else {
            // 拒绝归还，只更新审批状态
            await database.rejectReturn(borrowing_id, approvalData);
        }

        // 记录日志
        await database.logAction('APPROVE_RETURN', admin_id, `${approved ? '同意' : '拒绝'}归还申请: 借用ID ${borrowing_id}`, req.ip);

        res.json({
            success: true,
            message: approved ? '归还申请已同意' : '归还申请已拒绝'
        });

    } catch (error) {
        console.error('审批归还申请错误:', error);
        res.status(500).json({
            success: false,
            message: '审批失败'
        });
    }
});

// 管理员API - 获取所有借用记录
app.get('/api/admin/borrowings', async (req, res) => {
    try {
        const borrowings = await database.getAllBorrowings();
        res.json({
            success: true,
            borrowings
        });
    } catch (error) {
        console.error('获取所有借用记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取借用记录失败'
        });
    }
});

// 管理员API - 获取当前借用状态
app.get('/api/admin/current-status', async (req, res) => {
    try {
        const currentBorrowings = await database.getCurrentBorrowings();
        res.json({
            success: true,
            borrowings: currentBorrowings
        });
    } catch (error) {
        console.error('获取当前状态错误:', error);
        res.status(500).json({
            success: false,
            message: '获取当前状态失败'
        });
    }
});

// 管理员API - 获取逾期记录
app.get('/api/admin/overdue', async (req, res) => {
    try {
        const overdueStats = await overdueChecker.getOverdueStats();
        res.json({
            success: true,
            overdue: overdueStats
        });
    } catch (error) {
        console.error('获取逾期记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取逾期记录失败'
        });
    }
});

// 管理员API - 手动生成逾期报告
app.post('/api/admin/generate-overdue-report', async (req, res) => {
    try {
        const report = await overdueChecker.generateOverdueReport();
        res.json({
            success: true,
            message: '逾期报告生成成功',
            report
        });
    } catch (error) {
        console.error('生成逾期报告错误:', error);
        res.status(500).json({
            success: false,
            message: '生成逾期报告失败'
        });
    }
});

// 管理员API - 手动生成月度全部借用记录报告
app.post('/api/admin/generate-monthly-report', async (req, res) => {
    try {
        const report = await monthlyReportGenerator.manualGenerate();
        res.json({
            success: true,
            message: '月度全部借用记录报告生成成功',
            report
        });
    } catch (error) {
        console.error('生成月度报告错误:', error);
        res.status(500).json({
            success: false,
            message: '生成月度报告失败'
        });
    }
});

// 管理员API - 获取月度报告统计信息
app.get('/api/admin/monthly-report-stats', async (req, res) => {
    try {
        const stats = await monthlyReportGenerator.getReportStats();
        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('获取月度报告统计错误:', error);
        res.status(500).json({
            success: false,
            message: '获取月度报告统计失败'
        });
    }
});

// 导出逾期报告Excel（支持手机端下载）
app.get('/api/admin/export-overdue-excel', async (req, res) => {
    try {
        const overdueRecords = await database.getOverdueBorrowings();
        const workbook = await generateOverdueExcel(overdueRecords);

        const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
        const filename = `逾期报告_${timestamp}.xlsx`;

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('导出逾期报告错误:', error);
        res.status(500).json({
            success: false,
            message: '导出失败'
        });
    }
});

// 导出所有记录Excel（支持筛选和手机端下载）
app.post('/api/admin/export-records-excel', async (req, res) => {
    try {
        const { vehicleType, month, year } = req.body;

        let records = await database.getAllBorrowings();

        // 按车辆类型筛选
        if (vehicleType && vehicleType !== 'all') {
            records = records.filter(record => record.brand === vehicleType || record.model === vehicleType);
        }

        // 按月份筛选
        if (month && year) {
            records = records.filter(record => {
                const borrowDate = new Date(record.borrow_date);
                return borrowDate.getFullYear() === parseInt(year) &&
                       borrowDate.getMonth() + 1 === parseInt(month);
            });
        }

        const workbook = await generateRecordsExcel(records);

        const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
        const filterText = vehicleType && vehicleType !== 'all' ? `_${vehicleType}` : '';
        const monthText = month && year ? `_${year}-${month.padStart(2, '0')}` : '';
        const filename = `借用记录${filterText}${monthText}_${timestamp}.xlsx`;

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('导出记录错误:', error);
        res.status(500).json({
            success: false,
            message: '导出失败'
        });
    }
});

// 获取统计详情（支持点击查看详细信息）
app.get('/api/admin/stats-details/:type', async (req, res) => {
    try {
        const { type } = req.params;
        let data = [];

        switch (type) {
            case 'vehicles':
                // 获取所有车辆（包括借用中的）
                const sql = `SELECT * FROM vehicles ORDER BY id`;
                data = await database.allQuery(sql);
                break;
            case 'available':
                // 获取当前可用车辆
                data = await database.getAvailableVehicles();
                break;
            case 'current':
                data = await database.getCurrentBorrowings();
                break;
            case 'overdue':
                const overdueStats = await overdueChecker.getOverdueStats();
                data = overdueStats.details;
                break;
            case 'total':
                data = await database.getAllBorrowings();
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: '无效的统计类型'
                });
        }

        res.json({
            success: true,
            data,
            type
        });

    } catch (error) {
        console.error('获取统计详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取统计详情失败'
        });
    }
});

// 获取媒体文件（支持查看上传的图片和视频）
app.get('/api/media/:type/:filename', async (req, res) => {
    try {
        const { type, filename } = req.params;
        const uploadsPath = await networkDetector.getUploadPath();
        const filePath = path.join(uploadsPath, type, filename);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        // 设置正确的Content-Type
        const ext = path.extname(filename).toLowerCase();
        let contentType = 'application/octet-stream';

        if (['.jpg', '.jpeg', '.png', '.gif'].includes(ext)) {
            contentType = `image/${ext.substring(1)}`;
        } else if (['.mp4', '.mov', '.avi', '.wmv'].includes(ext)) {
            contentType = `video/${ext.substring(1)}`;
        }

        res.setHeader('Content-Type', contentType);
        res.setHeader('Cache-Control', 'public, max-age=31536000');

        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);

    } catch (error) {
        console.error('获取媒体文件错误:', error);
        res.status(500).json({
            success: false,
            message: '获取文件失败'
        });
    }
});

// 获取网络状态
app.get('/api/network-status', (req, res) => {
    const status = networkDetector.getNetworkStatus();
    res.json({
        success: true,
        network: status
    });
});

// 初始化应用
async function initializeApp() {
    try {
        console.log('🚀 正在启动华域视觉数字技术部-车辆借用管理系统...');
        
        // 检测网络环境并创建目录
        await networkDetector.ensureDirectories();
        
        // 初始化数据库
        await database.initialize();
        
        // 启动逾期检查器
        overdueChecker = new OverdueChecker(database, networkDetector);
        overdueChecker.start();

        // 启动月度报告生成器
        monthlyReportGenerator = new MonthlyReportGenerator(database, networkDetector);
        monthlyReportGenerator.start();

        console.log('✅ 系统初始化完成');
        
    } catch (error) {
        console.error('❌ 系统初始化失败:', error);
        process.exit(1);
    }
}

// 启动服务器
app.listen(PORT, async () => {
    await initializeApp();
    console.log(`🌐 服务器运行在: http://localhost:${PORT}`);
    console.log(`📱 移动端访问: http://[您的IP]:${PORT}`);
    console.log('🎉 华域视觉数字技术部-车辆借用管理系统已启动');
});

// Excel生成函数
async function generateOverdueExcel(overdueRecords) {
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('逾期报告');

    // 设置表头
    worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '工号', key: 'employee_id', width: 12 },
        { header: '姓名', key: 'employee_name', width: 12 },
        { header: '车牌号', key: 'plate_number', width: 12 },
        { header: '车型', key: 'model', width: 15 },
        { header: '品牌', key: 'brand', width: 10 },
        { header: '借车时间', key: 'borrow_date', width: 18 },
        { header: '应还时间', key: 'return_date', width: 18 },
        { header: '逾期天数', key: 'overdue_days', width: 10 },
        { header: '备注', key: 'notes', width: 20 }
    ];

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFF' } };
    worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
    };

    // 添加数据
    overdueRecords.forEach((record, index) => {
        const borrowDate = moment(record.borrow_date);
        const returnDate = moment(record.return_date);
        const now = moment();
        const overdueDays = now.diff(returnDate, 'days');

        worksheet.addRow({
            index: index + 1,
            employee_id: record.employee_id,
            employee_name: record.employee_name,
            plate_number: record.plate_number,
            model: record.model,
            brand: record.brand,
            borrow_date: borrowDate.format('YYYY-MM-DD HH:mm'),
            return_date: returnDate.format('YYYY-MM-DD HH:mm'),
            overdue_days: overdueDays,
            notes: record.notes || ''
        });
    });

    return workbook;
}

async function generateRecordsExcel(records) {
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('借用记录');

    // 设置表头
    worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '工号', key: 'employee_id', width: 12 },
        { header: '姓名', key: 'employee_name', width: 12 },
        { header: '外部门工号', key: 'external_employee_id', width: 12 },
        { header: '外部门姓名', key: 'external_employee_name', width: 12 },
        { header: '车牌号', key: 'plate_number', width: 12 },
        { header: '车型', key: 'model', width: 15 },
        { header: '品牌', key: 'brand', width: 10 },
        { header: '测试类型', key: 'test_type', width: 10 },
        { header: '安全员', key: 'safety_officer', width: 20 },
        { header: '借车时间', key: 'borrow_date', width: 18 },
        { header: '预计归还', key: 'return_date', width: 18 },
        { header: '实际归还', key: 'actual_return_date', width: 18 },
        { header: '状态', key: 'status', width: 10 },
        { header: '备注', key: 'notes', width: 20 },
        { header: '借车图片', key: 'borrow_media', width: 15 },
        { header: '还车图片', key: 'return_media', width: 15 }
    ];

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFF' } };
    worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '2E8B57' }
    };

    // 添加数据
    for (let i = 0; i < records.length; i++) {
        const record = records[i];
        const borrowDate = moment(record.borrow_date);
        const returnDate = moment(record.return_date);
        const actualReturnDate = record.actual_return_date ?
            moment(record.actual_return_date) : null;

        // 处理安全员信息（支持多人）
        let safetyOfficerText = '';
        if (record.safety_officer) {
            try {
                // 尝试解析JSON格式的多人安全员
                const officers = JSON.parse(record.safety_officer);
                if (Array.isArray(officers)) {
                    safetyOfficerText = officers.join(', ');
                } else {
                    safetyOfficerText = record.safety_officer;
                }
            } catch (e) {
                // 如果不是JSON格式，直接使用原始值
                safetyOfficerText = record.safety_officer;
            }
        }

        const rowData = {
            index: i + 1,
            employee_id: record.employee_id,
            employee_name: record.employee_name,
            external_employee_id: record.external_employee_id || '',
            external_employee_name: record.external_employee_name || '',
            plate_number: record.plate_number,
            model: record.model,
            brand: record.brand,
            test_type: record.test_type === 'dynamic' ? '动态测试' : '静态测试',
            safety_officer: safetyOfficerText,
            borrow_date: borrowDate.format('YYYY-MM-DD HH:mm'),
            return_date: returnDate.format('YYYY-MM-DD HH:mm'),
            actual_return_date: actualReturnDate ? actualReturnDate.format('YYYY-MM-DD HH:mm') : '未归还',
            status: record.status === 'borrowed' ? '借用中' : '已归还',
            notes: record.notes || '',
            borrow_media: record.borrow_media_path ? path.basename(record.borrow_media_path) : '',
            return_media: record.return_media_path ? path.basename(record.return_media_path) : ''
        };

        const row = worksheet.addRow(rowData);

        // 尝试嵌入图片（如果是图片文件）
        if (record.borrow_media_path && record.borrow_media_type === 'image') {
            try {
                if (fs.existsSync(record.borrow_media_path)) {
                    const imageId = workbook.addImage({
                        filename: record.borrow_media_path,
                        extension: path.extname(record.borrow_media_path).substring(1)
                    });

                    worksheet.addImage(imageId, {
                        tl: { col: 11, row: i + 1 },
                        ext: { width: 100, height: 75 }
                    });

                    row.height = 80;
                }
            } catch (error) {
                console.log('添加借车图片失败:', error.message);
            }
        }

        if (record.return_media_path && record.return_media_type === 'image') {
            try {
                if (fs.existsSync(record.return_media_path)) {
                    const imageId = workbook.addImage({
                        filename: record.return_media_path,
                        extension: path.extname(record.return_media_path).substring(1)
                    });

                    worksheet.addImage(imageId, {
                        tl: { col: 12, row: i + 1 },
                        ext: { width: 100, height: 75 }
                    });

                    row.height = 80;
                }
            } catch (error) {
                console.log('添加还车图片失败:', error.message);
            }
        }
    }

    return workbook;
}

// 移动多个文件到正确的车辆文件夹位置
async function moveMultipleFilesToCorrectLocation(req, actionType, data) {
    try {
        const uploadsPath = await networkDetector.getUploadPath();
        const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
        const actionTypeChinese = actionType === 'borrow' ? '借车' : '还车';

        // 获取车辆信息
        let vehicleFolderName = 'unknown_vehicle';

        if (actionType === 'borrow' && data.vehicle_id) {
            // 借车时根据vehicle_id获取车辆信息
            const vehicle = await database.getVehicleById(data.vehicle_id);
            if (vehicle) {
                vehicleFolderName = `${vehicle.brand}_${vehicle.model}_${vehicle.plate_number}`;
            }
        } else if (actionType === 'return' && data.borrowing_id) {
            // 还车时根据borrowing_id获取车辆信息
            const borrowing = await database.getBorrowingById(data.borrowing_id);
            if (borrowing) {
                const vehicle = await database.getVehicleById(borrowing.vehicle_id);
                if (vehicle) {
                    vehicleFolderName = `${vehicle.brand}_${vehicle.model}_${vehicle.plate_number}`;
                }
            }
        }

        // 创建最终的文件夹路径
        let operationFolderName;
        if (data.employee_id === '001') {
            // 外部门人员特殊命名：包含姓名和工号（如果有）
            const externalInfo = data.external_employee_name || data.employee_name;
            const externalId = data.external_employee_id ? `_${data.external_employee_id}` : '';
            operationFolderName = `${timestamp}_外部门人员(001)_${externalInfo}${externalId}_${actionTypeChinese}`;
        } else {
            // 普通员工命名
            operationFolderName = `${timestamp}_${data.employee_name}(${data.employee_id})_${actionTypeChinese}`;
        }
        const finalFolderPath = path.join(uploadsPath, vehicleFolderName, operationFolderName);

        // 确保最终文件夹存在
        await fs.ensureDir(finalFolderPath);

        const finalPaths = [];

        // 移动所有文件
        for (let i = 0; i < req.files.length; i++) {
            const file = req.files[i];
            const oldFilePath = file.path;
            const mediaType = file.mimetype.startsWith('image/') ? '图片' : '视频';
            const ext = path.extname(file.originalname);
            const fileName = `${mediaType}_${i + 1}${ext}`;
            const newFilePath = path.join(finalFolderPath, fileName);

            await fs.move(oldFilePath, newFilePath);

            finalPaths.push({
                path: newFilePath,
                type: file.mimetype.startsWith('image/') ? 'image' : 'video',
                originalName: file.originalname,
                size: file.size
            });
        }

        console.log(`✅ ${finalPaths.length}个文件移动成功: ${vehicleFolderName}/${operationFolderName}`);
        return finalPaths;

    } catch (error) {
        console.error('移动多文件失败:', error);
        throw error;
    }
}

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    if (overdueChecker) {
        overdueChecker.stop();
    }
    database.close();
    process.exit(0);
});
