// 管理员界面JavaScript
class AdminManager {
    constructor() {
        this.user = null;
        this.allRecords = [];
        
        this.init();
    }

    init() {
        this.checkAuth();
        this.bindEvents();
        this.loadUserInfo();
        this.loadStats();
        this.loadCurrentBorrowings();
        this.loadOverdueRecords();
        this.loadAllRecords();
        this.checkNetworkStatus();
    }

    // 检查管理员权限
    checkAuth() {
        const user = localStorage.getItem('user');
        if (!user) {
            window.location.href = '/';
            return;
        }
        
        try {
            this.user = JSON.parse(user);
            if (!this.user.isAdmin) {
                window.location.href = '/dashboard.html';
                return;
            }
        } catch (error) {
            localStorage.removeItem('user');
            window.location.href = '/';
        }
    }

    // 绑定事件
    bindEvents() {
        // 标签切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // 刷新按钮
        document.getElementById('refreshCurrent').addEventListener('click', () => {
            this.loadCurrentBorrowings();
        });

        document.getElementById('refreshOverdue').addEventListener('click', () => {
            this.loadOverdueRecords();
        });

        document.getElementById('refreshRecords').addEventListener('click', () => {
            this.loadAllRecords();
        });

        // 生成逾期报告
        document.getElementById('generateOverdueReport').addEventListener('click', () => {
            this.generateOverdueReport();
        });

        // 导出按钮
        document.getElementById('exportOverdueReport').addEventListener('click', () => {
            this.exportOverdueReport();
        });

        document.getElementById('exportAllRecords').addEventListener('click', () => {
            this.exportAllRecords();
        });

        document.getElementById('exportVehicleStats').addEventListener('click', () => {
            this.exportVehicleStats();
        });

        document.getElementById('exportUserStats').addEventListener('click', () => {
            this.exportUserStats();
        });

        // 统计详情模态框
        document.getElementById('closeStatsModal').addEventListener('click', () => {
            this.closeStatsModal();
        });

        document.getElementById('statsModal').addEventListener('click', (e) => {
            if (e.target.id === 'statsModal') {
                this.closeStatsModal();
            }
        });

        // 筛选面板切换
        document.getElementById('toggleFilters').addEventListener('click', () => {
            this.toggleFiltersPanel();
        });

        // 日期范围筛选
        document.getElementById('dateRangeFilter').addEventListener('change', () => {
            this.toggleCustomDateInputs();
        });

        // 应用筛选
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.applyFilters();
        });

        // 清除筛选
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // 审批模态框事件
        const approvalModal = document.getElementById('approvalModal');
        if (approvalModal) {
            // 点击模态框外部关闭
            approvalModal.addEventListener('click', (e) => {
                if (e.target.id === 'approvalModal') {
                    this.closeApprovalModal();
                }
            });
        }

        // 审批模态框关闭按钮
        const closeApprovalBtn = document.getElementById('closeApprovalModal');
        if (closeApprovalBtn) {
            closeApprovalBtn.addEventListener('click', () => {
                this.closeApprovalModal();
            });
        }

        // ESC键关闭审批模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeStatsModal();
                this.closeApprovalModal();
            }
        });
    }

    // 加载用户信息
    loadUserInfo() {
        document.getElementById('userName').textContent = this.user.name;
    }

    // 切换标签
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        // 根据标签加载相应数据
        if (tabName === 'current') {
            this.loadCurrentBorrowings();
        } else if (tabName === 'overdue') {
            this.loadOverdueRecords();
        } else if (tabName === 'records') {
            this.loadAllRecords();
        } else if (tabName === 'reports') {
            this.loadReportsInfo();
        }
    }

    // 加载统计数据
    async loadStats() {
        try {
            // 获取所有车辆状态（包含总数和可用数）
            const allVehiclesResponse = await fetch('/api/vehicles/status');
            const allVehiclesData = await allVehiclesResponse.json();

            // 获取可用车辆
            const availableVehiclesResponse = await fetch('/api/vehicles');
            const availableVehiclesData = await availableVehiclesResponse.json();

            // 获取当前借用
            const currentResponse = await fetch('/api/admin/current-status');
            const currentData = await currentResponse.json();

            // 获取逾期记录
            const overdueResponse = await fetch('/api/admin/overdue');
            const overdueData = await overdueResponse.json();

            // 获取所有记录
            const allResponse = await fetch('/api/admin/borrowings');
            const allData = await allResponse.json();

            // 获取待审批归还
            const pendingResponse = await fetch('/api/admin/pending-returns');
            const pendingData = await pendingResponse.json();

            // 更新统计卡片
            document.getElementById('totalVehicles').textContent = allVehiclesData.success ? allVehiclesData.vehicles.length : '0';
            document.getElementById('availableVehicles').textContent = availableVehiclesData.success ? availableVehiclesData.vehicles.length : '0';
            document.getElementById('currentBorrowings').textContent = currentData.success ? currentData.borrowings.length : '0';
            document.getElementById('overdueCount').textContent = overdueData.success ? overdueData.overdue.total : '0';
            document.getElementById('totalRecords').textContent = allData.success ? allData.borrowings.length : '0';
            document.getElementById('pendingReturns').textContent = pendingData.success ? pendingData.requests.length : '0';

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 加载当前借用记录
    async loadCurrentBorrowings() {
        const container = document.getElementById('currentBorrowingsList');
        container.innerHTML = '<div class="loading-message">正在加载当前借用记录...</div>';

        try {
            const response = await fetch('/api/admin/current-status');
            const data = await response.json();

            if (data.success) {
                if (data.borrowings.length === 0) {
                    container.innerHTML = '<div class="loading-message">当前没有借用记录</div>';
                    return;
                }

                container.innerHTML = '';
                data.borrowings.forEach(borrowing => {
                    const item = this.createAdminBorrowingItem(borrowing);
                    container.appendChild(item);
                });
            }
        } catch (error) {
            console.error('加载当前借用记录失败:', error);
            container.innerHTML = '<div class="loading-message">加载失败，请稍后重试</div>';
        }
    }

    // 加载逾期记录
    async loadOverdueRecords() {
        const container = document.getElementById('overdueList');
        const statsContainer = document.getElementById('overdueStats');
        
        container.innerHTML = '<div class="loading-message">正在加载逾期记录...</div>';

        try {
            const response = await fetch('/api/admin/overdue');
            const data = await response.json();

            if (data.success) {
                // 更新逾期统计
                document.getElementById('totalOverdue').textContent = data.overdue.total;
                document.getElementById('criticalOverdue').textContent = data.overdue.critical;
                document.getElementById('warningOverdue').textContent = data.overdue.warning;
                document.getElementById('lightOverdue').textContent = data.overdue.light;
                document.getElementById('todayOverdue').textContent = data.overdue.today;

                if (data.overdue.details.length === 0) {
                    container.innerHTML = '<div class="loading-message">暂无逾期记录</div>';
                    return;
                }

                container.innerHTML = '';
                data.overdue.details.forEach(borrowing => {
                    const item = this.createOverdueBorrowingItem(borrowing);
                    container.appendChild(item);
                });
            }
        } catch (error) {
            console.error('加载逾期记录失败:', error);
            container.innerHTML = '<div class="loading-message">加载失败，请稍后重试</div>';
        }
    }

    // 加载所有记录
    async loadAllRecords() {
        const container = document.getElementById('allRecordsList');
        container.innerHTML = '<div class="loading-message">正在加载所有记录...</div>';

        try {
            const response = await fetch('/api/admin/borrowings');
            const data = await response.json();

            if (data.success) {
                if (data.borrowings.length === 0) {
                    container.innerHTML = '<div class="loading-message">暂无借用记录</div>';
                    return;
                }

                this.allRecords = data.borrowings;
                this.displayAllRecords(data.borrowings);
                await this.loadFilterOptions(data.borrowings);
            }
        } catch (error) {
            console.error('加载所有记录失败:', error);
            container.innerHTML = '<div class="loading-message">加载失败，请稍后重试</div>';
        }
    }

    // 显示所有记录
    displayAllRecords(records) {
        const container = document.getElementById('allRecordsList');
        container.innerHTML = '';

        records.forEach(record => {
            const item = this.createAdminBorrowingItem(record);
            container.appendChild(item);
        });
    }

    // 加载筛选选项
    async loadFilterOptions(records) {
        // 从数据库获取所有车辆信息
        try {
            const vehiclesResponse = await fetch('/api/vehicles/all');
            const vehiclesData = await vehiclesResponse.json();

            if (vehiclesData.success) {
                // 加载车辆类型选项（车牌号 - 品牌 型号）
                const vehicleTypes = vehiclesData.vehicles.map(vehicle => {
                    const vehicleInfo = `${vehicle.plate_number} - ${vehicle.brand} ${vehicle.model}`;
                    return {
                        value: vehicle.plate_number,
                        display: vehicleInfo
                    };
                }).sort((a, b) => a.display.localeCompare(b.display));

                const vehicleTypeFilter = document.getElementById('vehicleTypeFilter');
                vehicleTypeFilter.innerHTML = '<option value="">全部车辆</option>';
                vehicleTypes.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.value;
                    option.textContent = vehicle.display;
                    vehicleTypeFilter.appendChild(option);
                });
            }
        } catch (error) {
            console.error('获取车辆信息失败:', error);
        }

        // 加载借用人选项（规范化显示格式）
        const borrowers = [...new Set(records.map(record => {
            if (record.external_employee_name) {
                // 外部人员：姓名(外部)
                return `${record.external_employee_name}(外部)`;
            }
            // 内部员工：姓名(工号)
            return `${record.employee_name}(${record.employee_id})`;
        }))].sort();

        const borrowerFilter = document.getElementById('borrowerFilter');
        borrowerFilter.innerHTML = '<option value="">全部借用人</option>';
        borrowers.forEach(borrower => {
            const option = document.createElement('option');
            option.value = borrower;
            option.textContent = borrower;
            borrowerFilter.appendChild(option);
        });
    }

    // 切换筛选面板显示
    toggleFiltersPanel() {
        const panel = document.getElementById('filtersPanel');
        const btn = document.getElementById('toggleFilters');

        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            btn.textContent = '🔍 隐藏筛选';
        } else {
            panel.style.display = 'none';
            btn.textContent = '🔍 筛选';
        }
    }

    // 切换自定义日期输入框
    toggleCustomDateInputs() {
        const dateRange = document.getElementById('dateRangeFilter').value;
        const customGroups = document.querySelectorAll('.custom-date-group');

        if (dateRange === 'custom') {
            customGroups.forEach(group => group.style.display = 'flex');
        } else {
            customGroups.forEach(group => group.style.display = 'none');
        }
    }

    // 应用筛选
    applyFilters() {
        if (!this.allRecords) {
            return;
        }

        let filteredRecords = [...this.allRecords];

        // 状态筛选
        const statusFilter = document.getElementById('statusFilter').value;
        if (statusFilter) {
            filteredRecords = filteredRecords.filter(record => record.status === statusFilter);
        }

        // 车辆类型筛选（按车牌号筛选）
        const vehicleTypeFilter = document.getElementById('vehicleTypeFilter').value;
        if (vehicleTypeFilter) {
            filteredRecords = filteredRecords.filter(record =>
                record.plate_number === vehicleTypeFilter
            );
        }

        // 借用人筛选（规范化格式）
        const borrowerFilter = document.getElementById('borrowerFilter').value;
        if (borrowerFilter) {
            filteredRecords = filteredRecords.filter(record => {
                const recordBorrower = record.external_employee_name
                    ? `${record.external_employee_name}(外部)`
                    : `${record.employee_name}(${record.employee_id})`;
                return recordBorrower === borrowerFilter;
            });
        }

        // 日期筛选
        const dateRangeFilter = document.getElementById('dateRangeFilter').value;
        if (dateRangeFilter) {
            filteredRecords = this.filterByDateRange(filteredRecords, dateRangeFilter);
        }

        this.displayAllRecords(filteredRecords);
        this.showMessage(`筛选完成，共找到 ${filteredRecords.length} 条记录`, 'info');
    }

    // 按日期范围筛选
    filterByDateRange(records, dateRange) {
        const now = new Date();
        let startDate, endDate;

        switch (dateRange) {
            case 'today':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                break;
            case '30days':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                endDate = now;
                break;
            case 'thisMonth':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                break;
            case 'lastMonth':
                startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                endDate = new Date(now.getFullYear(), now.getMonth(), 0);
                break;
            case 'custom':
                const startDateInput = document.getElementById('startDate').value;
                const endDateInput = document.getElementById('endDate').value;

                if (!startDateInput) {
                    this.showMessage('请选择开始日期', 'error');
                    return records;
                }

                startDate = new Date(startDateInput);
                endDate = endDateInput ? new Date(endDateInput + 'T23:59:59') : now;
                break;
            default:
                return records;
        }

        return records.filter(record => {
            const borrowDate = new Date(record.borrow_date);
            return borrowDate >= startDate && borrowDate <= endDate;
        });
    }

    // 清除筛选
    clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('vehicleTypeFilter').value = '';
        document.getElementById('borrowerFilter').value = '';
        document.getElementById('dateRangeFilter').value = '';
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';

        this.toggleCustomDateInputs();
        this.displayAllRecords(this.allRecords);
        this.showMessage('筛选已清除', 'info');
    }

    // 创建管理员借用记录项
    createAdminBorrowingItem(borrowing) {
        const div = document.createElement('div');
        div.className = 'admin-borrowing-item';
        
        const borrowDate = new Date(borrowing.borrow_date).toLocaleString();
        const returnDate = new Date(borrowing.return_date).toLocaleString();
        const actualReturnDate = borrowing.actual_return_date ? 
            new Date(borrowing.actual_return_date).toLocaleString() : '未归还';

        // 检查是否逾期
        const now = new Date();
        const expectedReturn = new Date(borrowing.return_date);
        const isOverdue = borrowing.status === 'borrowed' && now > expectedReturn;
        const overdueDays = isOverdue ? Math.floor((now - expectedReturn) / (1000 * 60 * 60 * 24)) : 0;

        if (isOverdue) {
            if (overdueDays > 7) {
                div.classList.add('critical-overdue');
            } else if (overdueDays >= 3) {
                div.classList.add('warning-overdue');
            } else if (overdueDays >= 1) {
                div.classList.add('light-overdue');
            } else {
                div.classList.add('today-overdue');
            }
        }

        div.innerHTML = `
            <div class="borrowing-header">
                <div class="borrowing-title">
                    🚗 ${borrowing.plate_number} - ${borrowing.brand} ${borrowing.model}
                </div>
                <div class="borrowing-meta">
                    <span class="status-badge ${borrowing.status}">${borrowing.status === 'borrowed' ? '借用中' : '已归还'}</span>
                    ${isOverdue ? `<span class="overdue-badge ${this.getOverdueBadgeClass(overdueDays)}">逾期${overdueDays}天</span>` : ''}
                </div>
            </div>
            <div class="borrowing-details">
                <div class="detail-item">
                    <span class="detail-label">借用人</span>
                    <span class="detail-value">${borrowing.employee_name} (${borrowing.employee_id})</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">借车时间</span>
                    <span class="detail-value">${borrowDate}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">预计归还</span>
                    <span class="detail-value ${isOverdue ? 'overdue' : ''}">${returnDate}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">实际归还</span>
                    <span class="detail-value">${actualReturnDate}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">借车理由</span>
                    <span class="detail-value">${borrowing.notes || '无'}</span>
                </div>
            </div>
        `;

        return div;
    }

    // 获取逾期徽章样式类
    getOverdueBadgeClass(overdueDays) {
        if (overdueDays > 7) {
            return 'critical';
        } else if (overdueDays >= 3) {
            return 'warning';
        } else if (overdueDays >= 1) {
            return 'light';
        } else {
            return 'today';
        }
    }

    // 创建逾期借用记录项
    createOverdueBorrowingItem(borrowing) {
        const item = this.createAdminBorrowingItem(borrowing);

        // 添加联系按钮
        const header = item.querySelector('.borrowing-header');
        const contactBtn = document.createElement('button');
        contactBtn.className = 'action-btn';
        contactBtn.textContent = '联系用户';
        contactBtn.onclick = () => this.contactUser(borrowing);

        const metaDiv = header.querySelector('.borrowing-meta');
        metaDiv.appendChild(contactBtn);

        return item;
    }

    // 联系用户
    contactUser(borrowing) {
        const message = `提醒：您借用的车辆 ${borrowing.plate_number} 已逾期，请尽快归还。\n\n借用人：${borrowing.employee_name}\n工号：${borrowing.employee_id}\n预计归还时间：${new Date(borrowing.return_date).toLocaleString()}`;
        
        // 这里可以集成短信或邮件发送功能
        alert(message);
        this.showMessage('已记录联系操作', 'info');
    }

    // 生成逾期报告
    async generateOverdueReport() {
        const btn = document.getElementById('generateOverdueReport');
        const originalText = btn.textContent;
        
        btn.disabled = true;
        btn.textContent = '生成中...';

        try {
            const response = await fetch('/api/admin/generate-overdue-report', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage('逾期报告生成成功！', 'success');
                this.loadOverdueRecords(); // 刷新数据
            } else {
                this.showMessage(data.message || '生成报告失败', 'error');
            }
        } catch (error) {
            console.error('生成逾期报告失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            btn.disabled = false;
            btn.textContent = originalText;
        }
    }

    // 显示统计详情
    async showStatsDetails(type) {
        const modal = document.getElementById('statsModal');
        const title = document.getElementById('statsModalTitle');
        const content = document.getElementById('statsModalContent');

        const titles = {
            vehicles: '🚗 所有车辆详情',
            available: '✅ 当前可用车辆详情',
            current: '📋 当前借用详情',
            overdue: '⚠️ 逾期记录详情',
            total: '📊 所有借用记录'
        };

        title.textContent = titles[type] || '详情';
        content.innerHTML = '<div class="loading-message">正在加载详情...</div>';
        modal.style.display = 'block';
        modal.classList.add('show');

        try {
            const response = await fetch(`/api/admin/stats-details/${type}`);
            const data = await response.json();

            if (data.success) {
                this.renderStatsDetails(data.data, data.type);
            } else {
                content.innerHTML = '<div class="loading-message">加载失败</div>';
            }
        } catch (error) {
            console.error('获取统计详情失败:', error);
            content.innerHTML = '<div class="loading-message">加载失败，请稍后重试</div>';
        }
    }

    // 渲染统计详情
    renderStatsDetails(data, type) {
        const content = document.getElementById('statsModalContent');

        if (data.length === 0) {
            content.innerHTML = '<div class="loading-message">暂无数据</div>';
            return;
        }

        content.innerHTML = '';

        data.forEach(item => {
            const detailItem = this.createDetailItem(item, type);
            content.appendChild(detailItem);
        });
    }

    // 创建详情项
    createDetailItem(item, type) {
        const div = document.createElement('div');
        div.className = 'stats-detail-item';

        if (type === 'vehicles' || type === 'available') {
            div.innerHTML = this.createVehicleDetail(item);
        } else {
            div.innerHTML = this.createBorrowingDetail(item, type);
        }

        return div;
    }

    // 创建车辆详情
    createVehicleDetail(vehicle) {
        // 安全获取字段值，避免undefined
        const plateNumber = vehicle.plate_number || '未知';
        const brand = vehicle.brand || '未知';
        const model = vehicle.model || '未知';
        const color = vehicle.color || '未知';
        const status = vehicle.status || 'unknown';
        const statusText = status === 'available' ? '可用' : status === 'borrowed' ? '借用中' : '未知';

        // 安全处理创建时间
        let createdTime = '未记录';
        if (vehicle.created_at) {
            try {
                const date = new Date(vehicle.created_at);
                if (!isNaN(date.getTime())) {
                    createdTime = date.toLocaleString();
                }
            } catch (e) {
                createdTime = '时间格式错误';
            }
        }

        return `
            <div class="detail-header">
                <div class="detail-title">🚗 ${plateNumber} - ${brand} ${model}</div>
                <span class="detail-status ${status}">${statusText}</span>
            </div>
            <div class="detail-grid">
                <div class="detail-field">
                    <span class="detail-field-label">车牌号</span>
                    <span class="detail-field-value">${plateNumber}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">品牌</span>
                    <span class="detail-field-value">${brand}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">型号</span>
                    <span class="detail-field-value">${model}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">颜色</span>
                    <span class="detail-field-value">${color}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">状态</span>
                    <span class="detail-field-value">${statusText}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">录入时间</span>
                    <span class="detail-field-value">${createdTime}</span>
                </div>
            </div>
        `;
    }

    // 创建借用详情
    createBorrowingDetail(borrowing, type) {
        // 安全处理日期，避免Invalid Date
        const borrowDate = this.safeFormatDate(borrowing.borrow_date, '未记录');
        const returnDate = this.safeFormatDate(borrowing.return_date, '未设置');
        const actualReturnDate = borrowing.actual_return_date ?
            this.safeFormatDate(borrowing.actual_return_date, '时间错误') : '未归还';

        let statusClass = borrowing.status;
        let statusText = borrowing.status === 'borrowed' ? '借用中' : '已归还';

        if (type === 'overdue') {
            statusClass = 'overdue';
            statusText = `逾期${borrowing.overdue_days}天`;
        }

        // 安全获取字段值
        const plateNumber = borrowing.plate_number || '未知车牌';
        const employeeName = borrowing.employee_name || '未知用户';
        const employeeId = borrowing.employee_id || '未知工号';
        const brand = borrowing.brand || '未知';
        const model = borrowing.model || '未知';

        return `
            <div class="detail-header">
                <div class="detail-title">🚗 ${plateNumber} - ${employeeName}</div>
                <span class="detail-status ${statusClass}">${statusText}</span>
            </div>
            <div class="detail-grid">
                <div class="detail-field">
                    <span class="detail-field-label">借用人</span>
                    <span class="detail-field-value">${employeeName} (${employeeId})</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">车辆</span>
                    <span class="detail-field-value">${plateNumber} - ${brand} ${model}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">借车时间</span>
                    <span class="detail-field-value">${borrowDate}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">预计归还</span>
                    <span class="detail-field-value">${returnDate}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">实际归还</span>
                    <span class="detail-field-value">${actualReturnDate}</span>
                </div>
                <div class="detail-field">
                    <span class="detail-field-label">借车理由</span>
                    <span class="detail-field-value">${borrowing.notes || '无'}</span>
                </div>
            </div>
            ${this.createMediaPreview(borrowing)}
        `;
    }

    // 安全格式化日期
    safeFormatDate(dateValue, defaultValue = '未知时间') {
        if (!dateValue) {
            return defaultValue;
        }

        try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) {
                return defaultValue;
            }
            return date.toLocaleString();
        } catch (error) {
            return defaultValue;
        }
    }

    // 创建媒体预览
    createMediaPreview(borrowing) {
        let mediaHtml = '<div class="media-preview">';

        // 借车媒体
        mediaHtml += '<div class="media-item">';
        mediaHtml += '<h4>借车时照片/视频</h4>';
        if (borrowing.borrow_media_path) {
            if (borrowing.borrow_media_type === 'multiple') {
                // 处理多文件（JSON格式）
                try {
                    const filePaths = JSON.parse(borrowing.borrow_media_path);
                    if (filePaths && filePaths.length > 0) {
                        mediaHtml += '<div class="media-grid">';
                        filePaths.forEach((filePath, index) => {
                            const filename = filePath.split(/[\\\/]/).pop();
                            const mediaUrl = `/api/media/borrow/${filename}`;

                            // 根据文件扩展名判断类型
                            const isImage = /\.(jpg|jpeg|png|gif)$/i.test(filePath);

                            if (isImage) {
                                mediaHtml += `<img src="${mediaUrl}" alt="借车照片 ${index + 1}" onclick="window.open('${mediaUrl}', '_blank')">`;
                            } else {
                                mediaHtml += `<video src="${mediaUrl}" controls onclick="window.open('${mediaUrl}', '_blank')"></video>`;
                            }
                        });
                        mediaHtml += '</div>';
                    } else {
                        mediaHtml += '<div class="no-media">无媒体文件</div>';
                    }
                } catch (e) {
                    console.error('解析借车媒体路径失败:', e);
                    mediaHtml += '<div class="no-media">媒体文件解析失败</div>';
                }
            } else {
                // 处理单文件（向后兼容）
                const filename = borrowing.borrow_media_path.split(/[\\\/]/).pop();
                const mediaUrl = `/api/media/borrow/${filename}`;

                if (borrowing.borrow_media_type === 'image') {
                    mediaHtml += `<img src="${mediaUrl}" alt="借车照片" onclick="window.open('${mediaUrl}', '_blank')">`;
                } else {
                    mediaHtml += `<video src="${mediaUrl}" controls onclick="window.open('${mediaUrl}', '_blank')"></video>`;
                }
            }
        } else {
            mediaHtml += '<div class="no-media">无媒体文件</div>';
        }
        mediaHtml += '</div>';

        // 还车媒体
        mediaHtml += '<div class="media-item">';
        mediaHtml += '<h4>还车时照片/视频</h4>';
        if (borrowing.return_media_path) {
            if (borrowing.return_media_type === 'multiple') {
                // 处理多文件（JSON格式）
                try {
                    const filePaths = JSON.parse(borrowing.return_media_path);
                    if (filePaths && filePaths.length > 0) {
                        mediaHtml += '<div class="media-grid">';
                        filePaths.forEach((filePath, index) => {
                            const filename = filePath.split(/[\\\/]/).pop();
                            const mediaUrl = `/api/media/return/${filename}`;

                            // 根据文件扩展名判断类型
                            const isImage = /\.(jpg|jpeg|png|gif)$/i.test(filePath);

                            if (isImage) {
                                mediaHtml += `<img src="${mediaUrl}" alt="还车照片 ${index + 1}" onclick="window.open('${mediaUrl}', '_blank')">`;
                            } else {
                                mediaHtml += `<video src="${mediaUrl}" controls onclick="window.open('${mediaUrl}', '_blank')"></video>`;
                            }
                        });
                        mediaHtml += '</div>';
                    } else {
                        mediaHtml += '<div class="no-media">无媒体文件</div>';
                    }
                } catch (e) {
                    console.error('解析还车媒体路径失败:', e);
                    mediaHtml += '<div class="no-media">媒体文件解析失败</div>';
                }
            } else {
                // 处理单文件（向后兼容）
                const filename = borrowing.return_media_path.split(/[\\\/]/).pop();
                const mediaUrl = `/api/media/return/${filename}`;

                if (borrowing.return_media_type === 'image') {
                    mediaHtml += `<img src="${mediaUrl}" alt="还车照片" onclick="window.open('${mediaUrl}', '_blank')">`;
                } else {
                    mediaHtml += `<video src="${mediaUrl}" controls onclick="window.open('${mediaUrl}', '_blank')"></video>`;
                }
            }
        } else {
            mediaHtml += '<div class="no-media">无媒体文件</div>';
        }
        mediaHtml += '</div>';

        mediaHtml += '</div>';
        return mediaHtml;
    }

    // 关闭统计详情模态框
    closeStatsModal() {
        const modal = document.getElementById('statsModal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
        }
    }

    // 导出逾期报告
    async exportOverdueReport() {
        const btn = document.getElementById('exportOverdueReport');
        const originalText = btn.textContent;

        btn.disabled = true;
        btn.textContent = '📥 导出中...';

        try {
            const response = await fetch('/api/admin/export-overdue-excel');

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = this.getFilenameFromResponse(response) || '逾期报告.xlsx';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showMessage('逾期报告导出成功！', 'success');
            } else {
                this.showMessage('导出失败，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('导出逾期报告失败:', error);
            this.showMessage('导出失败，请检查网络连接', 'error');
        } finally {
            btn.disabled = false;
            btn.textContent = originalText;
        }
    }

    // 导出所有记录
    async exportAllRecords() {
        const btn = document.getElementById('exportAllRecords');
        const originalText = btn.textContent;

        btn.disabled = true;
        btn.textContent = '📥 导出中...';

        try {
            const vehicleType = document.getElementById('exportVehicleType').value;
            const year = document.getElementById('exportYear').value;
            const month = document.getElementById('exportMonth').value;

            const response = await fetch('/api/admin/export-records-excel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    vehicleType: vehicleType === 'all' ? null : vehicleType,
                    year,
                    month
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = this.getFilenameFromResponse(response) || '借用记录.xlsx';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showMessage('借用记录导出成功！', 'success');
            } else {
                this.showMessage('导出失败，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('导出记录失败:', error);
            this.showMessage('导出失败，请检查网络连接', 'error');
        } finally {
            btn.disabled = false;
            btn.textContent = originalText;
        }
    }

    // 从响应头获取文件名
    getFilenameFromResponse(response) {
        const disposition = response.headers.get('Content-Disposition');
        if (disposition) {
            const matches = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (matches && matches[1]) {
                return decodeURIComponent(matches[1].replace(/['"]/g, ''));
            }
        }
        return null;
    }

    // 导出车辆统计
    async exportVehicleStats() {
        this.showMessage('车辆统计导出功能开发中...', 'info');
    }

    // 导出用户统计
    async exportUserStats() {
        this.showMessage('用户统计导出功能开发中...', 'info');
    }

    // 加载报告信息
    async loadReportsInfo() {
        try {
            const response = await fetch('/api/network-status');
            const data = await response.json();
            
            if (data.success) {
                const networkType = document.getElementById('networkType');
                const dataPath = document.getElementById('dataPath');
                const reportsPath = document.getElementById('reportsPath');
                
                networkType.textContent = data.network.isInternal ? '内网环境' : '外网环境';
                dataPath.textContent = data.network.dataPath;
                reportsPath.textContent = data.network.dataPath + '/reports';
            }
        } catch (error) {
            console.error('加载报告信息失败:', error);
        }
    }

    // 检查网络状态
    async checkNetworkStatus() {
        try {
            const response = await fetch('/api/network-status');
            const data = await response.json();
            
            if (data.success) {
                const statusDot = document.querySelector('.network-indicator .status-dot');
                const statusText = document.querySelector('.network-indicator .status-text');
                
                if (data.network.isInternal) {
                    statusDot.style.background = '#27ae60';
                    statusText.textContent = '内网环境';
                } else {
                    statusDot.style.background = '#f39c12';
                    statusText.textContent = '外网环境';
                }
            }
        } catch (error) {
            console.error('网络状态检查失败:', error);
        }
    }

    // 显示消息提示
    showMessage(message, type = 'info') {
        const toast = document.getElementById('messageToast');
        const icon = toast.querySelector('.toast-icon');
        const text = toast.querySelector('.toast-text');
        
        const icons = {
            success: '✅',
            error: '❌',
            info: 'ℹ️'
        };
        
        icon.textContent = icons[type] || icons.info;
        text.textContent = message;
        
        toast.className = `message-toast ${type}`;
        toast.classList.add('show');
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // 显示待审批归还列表
    async showPendingReturns() {
        try {
            const response = await fetch('/api/admin/pending-returns');
            const data = await response.json();

            if (data.success) {
                this.displayPendingReturns(data.requests);
            } else {
                this.showMessage('获取待审批申请失败', 'error');
            }
        } catch (error) {
            console.error('获取待审批申请失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        }
    }

    // 显示待审批归还列表
    displayPendingReturns(requests) {
        const modal = document.getElementById('statsModal');
        const title = document.getElementById('statsModalTitle');
        const content = document.getElementById('statsModalContent');

        title.textContent = '📋 待审批归还申请';

        if (requests.length === 0) {
            content.innerHTML = '<div class="no-data">暂无待审批的归还申请</div>';
        } else {
            const requestsHtml = requests.map(request => this.createPendingReturnItem(request)).join('');
            content.innerHTML = requestsHtml;
        }

        modal.style.display = 'block';
        modal.classList.add('show');
    }

    // 创建待审批归还项
    createPendingReturnItem(request) {
        const returnDate = this.safeFormatDate(request.actual_return_date, '未知时间');

        return `
            <div class="pending-return-item">
                <div class="pending-header">
                    <div class="pending-title">🚗 ${request.plate_number} - ${this.getDisplayName(request)}</div>
                    <button class="approve-btn" onclick="adminManager.openApprovalModal(${request.id})">审批</button>
                </div>
                <div class="pending-details">
                    <div class="detail-field">
                        <span class="detail-field-label">申请人</span>
                        <span class="detail-field-value">${this.getDisplayName(request)} (${request.employee_id})</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">车辆</span>
                        <span class="detail-field-value">${request.plate_number} - ${request.brand} ${request.model}</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">安全员</span>
                        <span class="detail-field-value">${request.safety_officer || '未填写'}</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">借车理由</span>
                        <span class="detail-field-value">${request.notes || '无'}</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">归还时间</span>
                        <span class="detail-field-value">${returnDate}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取显示名称（处理外部人员）
    getDisplayName(record) {
        if (record.external_employee_name) {
            return record.external_employee_name;
        }
        return record.employee_name;
    }

    // 打开审批模态框
    async openApprovalModal(borrowingId) {
        try {
            // 获取待审批申请详情
            const response = await fetch('/api/admin/pending-returns');
            const data = await response.json();

            if (data.success) {
                const request = data.requests.find(r => r.id === borrowingId);
                if (request) {
                    this.currentApprovalId = borrowingId;
                    this.displayApprovalDetails(request);
                    document.getElementById('approvalModal').style.display = 'block';
                } else {
                    this.showMessage('找不到申请记录', 'error');
                }
            }
        } catch (error) {
            console.error('获取申请详情失败:', error);
            this.showMessage('获取详情失败', 'error');
        }
    }

    // 显示审批详情
    displayApprovalDetails(request) {
        const content = document.getElementById('approvalContent');
        const returnDate = this.safeFormatDate(request.actual_return_date, '未知时间');

        content.innerHTML = `
            <div class="approval-details">
                <h4>归还申请详情</h4>
                <div class="detail-grid">
                    <div class="detail-field">
                        <span class="detail-field-label">申请人</span>
                        <span class="detail-field-value">${this.getDisplayName(request)} (${request.employee_id})</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">车辆</span>
                        <span class="detail-field-value">${request.plate_number} - ${request.brand} ${request.model}</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">安全员</span>
                        <span class="detail-field-value">${request.safety_officer || '未填写'}</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">借车理由</span>
                        <span class="detail-field-value">${request.notes || '无'}</span>
                    </div>
                    <div class="detail-field">
                        <span class="detail-field-label">归还时间</span>
                        <span class="detail-field-value">${returnDate}</span>
                    </div>
                    ${request.return_notes ? `
                    <div class="detail-field">
                        <span class="detail-field-label">还车备注</span>
                        <span class="detail-field-value">${request.return_notes}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // 审批归还申请
    async approveReturn(approved) {
        const notes = document.getElementById('approvalNotes').value.trim();

        if (!approved && !notes) {
            this.showMessage('拒绝归还时必须填写审批备注', 'error');
            return;
        }

        try {
            const response = await fetch('/api/admin/approve-return', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    borrowing_id: this.currentApprovalId,
                    approved,
                    admin_id: this.user.id,
                    notes
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage(approved ? '归还申请已同意' : '归还申请已拒绝', 'success');
                this.closeApprovalModal();
                this.loadStats(); // 刷新统计数据
                this.loadCurrentBorrowings(); // 刷新当前借用
            } else {
                this.showMessage(data.message || '审批失败', 'error');
            }
        } catch (error) {
            console.error('审批失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        }
    }

    // 关闭审批模态框
    closeApprovalModal() {
        const modal = document.getElementById('approvalModal');
        if (modal) {
            modal.style.display = 'none';
        }

        const notes = document.getElementById('approvalNotes');
        if (notes) {
            notes.value = '';
        }

        this.currentApprovalId = null;
    }

    // 退出登录
    logout() {
        localStorage.removeItem('user');
        localStorage.removeItem('lastEmployeeId');
        window.location.href = '/';
    }
}

// 全局实例
let adminManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    adminManager = new AdminManager();
});
