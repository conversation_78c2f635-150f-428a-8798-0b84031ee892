<p align="center">
  <a href="https://c2fo.io/fast-csv" target="blank"><img src="https://c2fo.io/fast-csv/img/logo.svg" width="200" alt="fast-csv Logo" /></a>
</p>

[![npm version](https://img.shields.io/npm/v/@fast-csv/format.svg)](https://www.npmjs.org/package/@fast-csv/format)
[![Build Status](https://travis-ci.org/C2FO/fast-csv.svg?branch=master)](https://travis-ci.org/C2FO/fast-csv)
[![Coverage Status](https://coveralls.io/repos/github/C2FO/fast-csv/badge.svg?branch=master)](https://coveralls.io/github/C2FO/fast-csv?branch=master)
[![Known Vulnerabilities](https://snyk.io/test/github/C2FO/fast-csv/badge.svg?targetFile=packages/format/package.json)](https://snyk.io/test/github/C2FO/fast-csv?targetFile=packages/format/package.json)

# `@fast-csv/format`

`fast-csv` package to format CSVs.

## Installation

[Install Guide](https://c2fo.io/fast-csv/docs/introduction/install)

## Usage

To get started with `@fast-csv/format` [check out the docs](https://c2fo.io/fast-csv/docs/formatting/getting-started)
