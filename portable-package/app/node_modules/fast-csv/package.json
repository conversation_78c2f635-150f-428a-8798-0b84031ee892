{"name": "fast-csv", "version": "4.3.6", "description": "CSV parser and writer", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run clean && npm run compile", "clean": "rm -rf ./build && rm -rf tsconfig.tsbuildinfo", "compile": "tsc"}, "directories": {"lib": "src", "test": "__tests__"}, "files": ["build/src/**"], "repository": {"type": "git", "url": "git+https://github.com/C2FO/fast-csv.git", "directory": "packages/fast-csv"}, "keywords": ["csv", "parser", "fast", "writer", "csv writer", "CSV"], "homepage": "http://c2fo.github.com/fast-csv", "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=10.0.0"}, "dependencies": {"@fast-csv/format": "4.3.5", "@fast-csv/parse": "4.3.6"}, "gitHead": "3dc859edb19924b315051e4c87d6273808a0de73"}