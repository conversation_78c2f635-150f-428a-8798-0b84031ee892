<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆筛选问题诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-section {
            background: white;
            border: 1px solid #ddd;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #2c3e50;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .diagnostic-step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        .diagnostic-step.error {
            border-left-color: #e74c3c;
        }
        .diagnostic-step.success {
            border-left-color: #27ae60;
        }
        .diagnostic-step.warning {
            border-left-color: #f39c12;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .result-box {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin: 5px;
        }
        .status-success {
            background: #d4edda;
            color: #27ae60;
        }
        .status-error {
            background: #f8d7da;
            color: #e74c3c;
        }
        .status-warning {
            background: #fff3cd;
            color: #f39c12;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "⏳ ";
            color: #f39c12;
            font-weight: bold;
        }
        .checklist li.success:before {
            content: "✅ ";
            color: #27ae60;
        }
        .checklist li.error:before {
            content: "❌ ";
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <h1>🔍 车辆筛选问题诊断工具</h1>
    
    <div class="diagnostic-section">
        <h2 class="section-title">📋 问题描述</h2>
        <p><strong>问题：</strong> 车辆类型筛选下拉框只显示"全部车辆"，没有显示具体的车辆选项。</p>
        <p><strong>预期：</strong> 应该显示所有车辆的"车牌号 - 品牌 型号"格式选项。</p>
    </div>

    <div class="diagnostic-section">
        <h2 class="section-title">🔧 诊断步骤</h2>
        
        <div class="diagnostic-step">
            <h4>步骤1: 检查API端点</h4>
            <p>测试 /api/vehicles/all 是否正常响应</p>
            <button class="test-button" onclick="checkAPI()">测试API</button>
            <div id="apiResult"></div>
        </div>
        
        <div class="diagnostic-step">
            <h4>步骤2: 检查数据库连接</h4>
            <p>验证是否能获取到车辆数据</p>
            <button class="test-button" onclick="checkDatabase()">检查数据库</button>
            <div id="databaseResult"></div>
        </div>
        
        <div class="diagnostic-step">
            <h4>步骤3: 检查前端逻辑</h4>
            <p>模拟前端筛选选项生成过程</p>
            <button class="test-button" onclick="checkFrontendLogic()">检查前端逻辑</button>
            <div id="frontendResult"></div>
        </div>
        
        <div class="diagnostic-step">
            <h4>步骤4: 检查浏览器控制台</h4>
            <p>查看是否有JavaScript错误</p>
            <button class="test-button" onclick="checkConsole()">检查控制台</button>
            <div id="consoleResult"></div>
        </div>
    </div>

    <div class="diagnostic-section">
        <h2 class="section-title">📊 诊断结果</h2>
        <div id="diagnosticSummary">
            <p>点击上方的诊断步骤开始检查...</p>
        </div>
    </div>

    <div class="diagnostic-section">
        <h2 class="section-title">🛠️ 可能的解决方案</h2>
        <ul class="checklist">
            <li id="solution1">重启服务器，确保新的API端点生效</li>
            <li id="solution2">清除浏览器缓存，刷新页面</li>
            <li id="solution3">检查数据库中是否有车辆数据</li>
            <li id="solution4">检查网络连接和API响应</li>
            <li id="solution5">查看浏览器开发者工具的控制台错误</li>
        </ul>
    </div>

    <script>
        let diagnosticResults = {
            api: null,
            database: null,
            frontend: null,
            console: null
        };

        async function checkAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>正在检查API...</p>';
            
            try {
                const response = await fetch('/api/vehicles/all');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    diagnosticResults.api = 'success';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-success">API正常</span>
                        <div class="result-box">状态码: ${response.status}
车辆数量: ${data.vehicles ? data.vehicles.length : 0}
响应数据: ${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    diagnosticResults.api = 'error';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-error">API异常</span>
                        <div class="result-box">状态码: ${response.status}
错误信息: ${data.message || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                diagnosticResults.api = 'error';
                resultDiv.innerHTML = `
                    <span class="status-indicator status-error">API请求失败</span>
                    <div class="result-box">错误: ${error.message}</div>
                `;
            }
            
            updateSummary();
        }

        async function checkDatabase() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.innerHTML = '<p>正在检查数据库...</p>';
            
            try {
                // 同时检查多个API端点
                const [allVehicles, availableVehicles, vehicleStatus] = await Promise.all([
                    fetch('/api/vehicles/all').then(r => r.json()),
                    fetch('/api/vehicles').then(r => r.json()),
                    fetch('/api/vehicles/status').then(r => r.json())
                ]);
                
                const hasData = allVehicles.success && allVehicles.vehicles && allVehicles.vehicles.length > 0;
                
                if (hasData) {
                    diagnosticResults.database = 'success';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-success">数据库正常</span>
                        <div class="result-box">所有车辆: ${allVehicles.vehicles.length}
可用车辆: ${availableVehicles.vehicles ? availableVehicles.vehicles.length : 0}
车辆状态: ${vehicleStatus.vehicles ? vehicleStatus.vehicles.length : 0}

车辆详情:
${JSON.stringify(allVehicles.vehicles, null, 2)}</div>
                    `;
                } else {
                    diagnosticResults.database = 'error';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-error">数据库无数据</span>
                        <div class="result-box">可能原因:
1. 数据库中没有车辆数据
2. 数据库连接失败
3. SQL查询错误</div>
                    `;
                }
            } catch (error) {
                diagnosticResults.database = 'error';
                resultDiv.innerHTML = `
                    <span class="status-indicator status-error">数据库检查失败</span>
                    <div class="result-box">错误: ${error.message}</div>
                `;
            }
            
            updateSummary();
        }

        async function checkFrontendLogic() {
            const resultDiv = document.getElementById('frontendResult');
            resultDiv.innerHTML = '<p>正在检查前端逻辑...</p>';
            
            try {
                const response = await fetch('/api/vehicles/all');
                const data = await response.json();
                
                if (data.success && data.vehicles && data.vehicles.length > 0) {
                    // 模拟前端处理逻辑
                    const vehicleTypes = data.vehicles.map(vehicle => {
                        const vehicleInfo = `${vehicle.plate_number} - ${vehicle.brand} ${vehicle.model}`;
                        return {
                            value: vehicle.plate_number,
                            display: vehicleInfo
                        };
                    }).sort((a, b) => a.display.localeCompare(b.display));
                    
                    diagnosticResults.frontend = 'success';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-success">前端逻辑正常</span>
                        <div class="result-box">生成的筛选选项:
${JSON.stringify(vehicleTypes, null, 2)}

HTML选项:
${vehicleTypes.map(v => `<option value="${v.value}">${v.display}</option>`).join('\n')}</div>
                    `;
                } else {
                    diagnosticResults.frontend = 'error';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-error">前端逻辑异常</span>
                        <div class="result-box">无法生成筛选选项，数据为空</div>
                    `;
                }
            } catch (error) {
                diagnosticResults.frontend = 'error';
                resultDiv.innerHTML = `
                    <span class="status-indicator status-error">前端逻辑检查失败</span>
                    <div class="result-box">错误: ${error.message}</div>
                `;
            }
            
            updateSummary();
        }

        function checkConsole() {
            const resultDiv = document.getElementById('consoleResult');
            diagnosticResults.console = 'warning';
            
            resultDiv.innerHTML = `
                <span class="status-indicator status-warning">需要手动检查</span>
                <div class="result-box">请按F12打开开发者工具，查看Console标签页：

1. 刷新管理员页面
2. 点击"所有记录"标签
3. 点击"🔍 筛选"按钮
4. 查看是否有红色错误信息

常见错误类型：
- 网络请求失败 (Failed to fetch)
- JavaScript语法错误
- API响应错误
- 权限问题</div>
            `;
            
            updateSummary();
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('diagnosticSummary');
            const results = Object.values(diagnosticResults).filter(r => r !== null);
            
            if (results.length === 0) {
                return;
            }
            
            const successCount = results.filter(r => r === 'success').length;
            const errorCount = results.filter(r => r === 'error').length;
            const warningCount = results.filter(r => r === 'warning').length;
            
            let summaryHtml = '<h4>诊断进度:</h4>';
            summaryHtml += `<p>已完成 ${results.length}/4 项检查</p>`;
            summaryHtml += `<p>✅ 正常: ${successCount} | ❌ 异常: ${errorCount} | ⚠️ 需检查: ${warningCount}</p>`;
            
            if (errorCount > 0) {
                summaryHtml += '<h4 style="color: #e74c3c;">发现问题:</h4>';
                if (diagnosticResults.api === 'error') {
                    summaryHtml += '<p>• API端点无法访问或返回错误</p>';
                }
                if (diagnosticResults.database === 'error') {
                    summaryHtml += '<p>• 数据库中没有车辆数据或连接失败</p>';
                }
                if (diagnosticResults.frontend === 'error') {
                    summaryHtml += '<p>• 前端逻辑处理异常</p>';
                }
            }
            
            if (successCount === 3 && warningCount === 1) {
                summaryHtml += '<h4 style="color: #27ae60;">建议操作:</h4>';
                summaryHtml += '<p>API和数据库都正常，请检查浏览器控制台是否有JavaScript错误，并尝试清除缓存后刷新页面。</p>';
            }
            
            summaryDiv.innerHTML = summaryHtml;
        }
    </script>
</body>
</html>
